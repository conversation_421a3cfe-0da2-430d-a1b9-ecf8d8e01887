2025/06/29-20:07:34.077 58cc Creating DB C:\Users\<USER>\OneDrive\Área de Trabalho\chatbot\.wwebjs_auth\session-whatsapp-business-bot\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb since it was missing.
2025/06/29-20:07:34.089 58cc Reusing MANIFEST C:\Users\<USER>\OneDrive\Área de Trabalho\chatbot\.wwebjs_auth\session-whatsapp-business-bot\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/06/29-20:07:34.497 3880 Level-0 table #5: started
2025/06/29-20:07:34.502 3880 Level-0 table #5: 17123 bytes OK
2025/06/29-20:07:34.503 3880 Delete type=0 #3
2025/06/29-20:07:34.503 3880 Manual compaction at level-0 from '\x00\x03\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x04\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.505 168c Level-0 table #7: started
2025/06/29-20:07:34.520 168c Level-0 table #7: 463 bytes OK
2025/06/29-20:07:34.521 168c Delete type=0 #4
2025/06/29-20:07:34.521 623c Manual compaction at level-0 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.521 623c Manual compaction at level-1 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at '\x00\x04\x00\x00\x05' @ 1048 : 0
2025/06/29-20:07:34.521 623c Compacting 1@1 + 1@2 files
2025/06/29-20:07:34.525 623c Generated table #8@1: 302 keys, 8192 bytes
2025/06/29-20:07:34.525 623c Compacted 1@1 + 1@2 files => 8192 bytes
2025/06/29-20:07:34.526 623c compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/29-20:07:34.526 623c Delete type=2 #7
2025/06/29-20:07:34.527 3880 Manual compaction at level-1 from '\x00\x04\x00\x00\x05' @ 1048 : 0 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.528 168c Level-0 table #10: started
2025/06/29-20:07:34.540 168c Level-0 table #10: 470 bytes OK
2025/06/29-20:07:34.541 168c Delete type=2 #5
2025/06/29-20:07:34.541 168c Delete type=0 #6
2025/06/29-20:07:34.542 168c Manual compaction at level-0 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.542 168c Manual compaction at level-1 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at '\x00\x05\x00\x00\x05' @ 1062 : 0
2025/06/29-20:07:34.542 168c Compacting 1@1 + 1@2 files
2025/06/29-20:07:34.546 168c Generated table #11@1: 299 keys, 8108 bytes
2025/06/29-20:07:34.546 168c Compacted 1@1 + 1@2 files => 8108 bytes
2025/06/29-20:07:34.547 168c compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/29-20:07:34.547 168c Delete type=2 #10
2025/06/29-20:07:34.548 623c Manual compaction at level-1 from '\x00\x05\x00\x00\x05' @ 1062 : 0 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.568 623c Level-0 table #13: started
2025/06/29-20:07:34.581 623c Level-0 table #13: 491 bytes OK
2025/06/29-20:07:34.582 623c Delete type=2 #8
2025/06/29-20:07:34.582 623c Delete type=0 #9
2025/06/29-20:07:34.583 623c Manual compaction at level-0 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.583 623c Manual compaction at level-1 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at '\x00\x06\x00\x00\x05' @ 1076 : 0
2025/06/29-20:07:34.583 623c Compacting 1@1 + 1@2 files
2025/06/29-20:07:34.587 623c Generated table #14@1: 296 keys, 8096 bytes
2025/06/29-20:07:34.587 623c Compacted 1@1 + 1@2 files => 8096 bytes
2025/06/29-20:07:34.588 623c compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/29-20:07:34.589 623c Delete type=2 #13
2025/06/29-20:07:34.589 623c Manual compaction at level-1 from '\x00\x06\x00\x00\x05' @ 1076 : 0 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.590 4e84 Level-0 table #16: started
2025/06/29-20:07:34.599 4e84 Level-0 table #16: 6614 bytes OK
2025/06/29-20:07:34.600 4e84 Delete type=2 #11
2025/06/29-20:07:34.600 4e84 Delete type=0 #12
2025/06/29-20:07:34.601 623c Manual compaction at level-0 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.601 623c Manual compaction at level-1 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at '\x00\x07\x00\x00\x05' @ 1340 : 0
2025/06/29-20:07:34.601 623c Compacting 1@1 + 1@2 files
2025/06/29-20:07:34.606 623c Generated table #17@1: 492 keys, 12819 bytes
2025/06/29-20:07:34.606 623c Compacted 1@1 + 1@2 files => 12819 bytes
2025/06/29-20:07:34.607 623c compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/29-20:07:34.607 623c Delete type=2 #16
2025/06/29-20:07:34.608 623c Manual compaction at level-1 from '\x00\x07\x00\x00\x05' @ 1340 : 0 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.609 623c Level-0 table #19: started
2025/06/29-20:07:34.623 623c Level-0 table #19: 1591 bytes OK
2025/06/29-20:07:34.625 623c Delete type=2 #14
2025/06/29-20:07:34.625 623c Delete type=0 #15
2025/06/29-20:07:34.626 623c Manual compaction at level-0 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.626 623c Manual compaction at level-1 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0e\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 1404 : 1
2025/06/29-20:07:34.626 623c Compacting 1@1 + 1@2 files
2025/06/29-20:07:34.631 623c Generated table #20@1: 544 keys, 14203 bytes
2025/06/29-20:07:34.631 623c Compacted 1@1 + 1@2 files => 14203 bytes
2025/06/29-20:07:34.631 623c compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/29-20:07:34.632 623c Delete type=2 #19
2025/06/29-20:07:34.632 623c Manual compaction at level-1 from '\x00\x0e\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 1404 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.633 3880 Level-0 table #22: started
2025/06/29-20:07:34.648 3880 Level-0 table #22: 2708 bytes OK
2025/06/29-20:07:34.649 3880 Delete type=2 #17
2025/06/29-20:07:34.649 3880 Delete type=0 #18
2025/06/29-20:07:34.650 3880 Manual compaction at level-0 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.650 3880 Manual compaction at level-1 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0f\x00\x00\xc8\x13\x00s\x00i\x00g\x00n\x00e\x00d\x00-\x00p\x00r\x00e\x00k\x00e\x00y\x00-\x00s\x00t\x00o\x00r\x00e' @ 1511 : 1
2025/06/29-20:07:34.650 3880 Compacting 1@1 + 1@2 files
2025/06/29-20:07:34.654 3880 Generated table #23@1: 695 keys, 16128 bytes
2025/06/29-20:07:34.654 3880 Compacted 1@1 + 1@2 files => 16128 bytes
2025/06/29-20:07:34.654 3880 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/29-20:07:34.655 3880 Delete type=2 #22
2025/06/29-20:07:34.655 3880 Manual compaction at level-1 from '\x00\x0f\x00\x00\xc8\x13\x00s\x00i\x00g\x00n\x00e\x00d\x00-\x00p\x00r\x00e\x00k\x00e\x00y\x00-\x00s\x00t\x00o\x00r\x00e' @ 1511 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.656 3880 Level-0 table #25: started
2025/06/29-20:07:34.668 3880 Level-0 table #25: 527 bytes OK
2025/06/29-20:07:34.669 3880 Delete type=2 #20
2025/06/29-20:07:34.669 3880 Delete type=0 #21
2025/06/29-20:07:34.670 3880 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.670 3880 Manual compaction at level-1 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 1592 : 1
2025/06/29-20:07:34.670 3880 Compacting 1@1 + 1@2 files
2025/06/29-20:07:34.674 3880 Generated table #26@1: 695 keys, 16097 bytes
2025/06/29-20:07:34.674 3880 Compacted 1@1 + 1@2 files => 16097 bytes
2025/06/29-20:07:34.675 3880 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/29-20:07:34.675 3880 Delete type=2 #25
2025/06/29-20:07:34.675 3880 Manual compaction at level-1 from '\x00\x10\x00\x00\x05' @ 1592 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.677 623c Level-0 table #28: started
2025/06/29-20:07:34.690 623c Level-0 table #28: 249 bytes OK
2025/06/29-20:07:34.692 623c Delete type=2 #23
2025/06/29-20:07:34.692 623c Delete type=0 #24
2025/06/29-20:07:34.694 623c Manual compaction at level-0 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.694 623c Manual compaction at level-1 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0c\x00\x00\x05' @ 1612 : 0
2025/06/29-20:07:34.694 623c Compacting 1@1 + 1@2 files
2025/06/29-20:07:34.698 623c Generated table #29@1: 689 keys, 16086 bytes
2025/06/29-20:07:34.698 623c Compacted 1@1 + 1@2 files => 16086 bytes
2025/06/29-20:07:34.699 623c compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/29-20:07:34.700 623c Delete type=2 #28
2025/06/29-20:07:34.700 623c Manual compaction at level-1 from '\x00\x0c\x00\x00\x05' @ 1612 : 0 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.701 623c Level-0 table #31: started
2025/06/29-20:07:34.713 623c Level-0 table #31: 249 bytes OK
2025/06/29-20:07:34.714 623c Delete type=2 #26
2025/06/29-20:07:34.714 623c Delete type=0 #27
2025/06/29-20:07:34.715 623c Manual compaction at level-0 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.715 623c Manual compaction at level-1 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0a\x00\x00\x05' @ 1618 : 0
2025/06/29-20:07:34.715 623c Compacting 1@1 + 1@2 files
2025/06/29-20:07:34.719 623c Generated table #32@1: 683 keys, 15912 bytes
2025/06/29-20:07:34.719 623c Compacted 1@1 + 1@2 files => 15912 bytes
2025/06/29-20:07:34.720 623c compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/29-20:07:34.721 623c Delete type=2 #31
2025/06/29-20:07:34.721 623c Manual compaction at level-1 from '\x00\x0a\x00\x00\x05' @ 1618 : 0 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.722 168c Level-0 table #34: started
2025/06/29-20:07:34.735 168c Level-0 table #34: 249 bytes OK
2025/06/29-20:07:34.736 168c Delete type=2 #29
2025/06/29-20:07:34.736 168c Delete type=0 #30
2025/06/29-20:07:34.737 168c Manual compaction at level-0 from '\x00\x0d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.737 168c Manual compaction at level-1 from '\x00\x0d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0d\x00\x00\x05' @ 1624 : 0
2025/06/29-20:07:34.737 168c Compacting 1@1 + 1@2 files
2025/06/29-20:07:34.741 168c Generated table #35@1: 677 keys, 15871 bytes
2025/06/29-20:07:34.741 168c Compacted 1@1 + 1@2 files => 15871 bytes
2025/06/29-20:07:34.741 168c compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/29-20:07:34.742 168c Delete type=2 #34
2025/06/29-20:07:34.742 168c Manual compaction at level-1 from '\x00\x0d\x00\x00\x05' @ 1624 : 0 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.745 168c Level-0 table #37: started
2025/06/29-20:07:34.760 168c Level-0 table #37: 1990 bytes OK
2025/06/29-20:07:34.762 168c Delete type=2 #32
2025/06/29-20:07:34.762 168c Delete type=0 #33
2025/06/29-20:07:34.764 168c Manual compaction at level-0 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/29-20:07:34.764 168c Manual compaction at level-1 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 1845 : 0
2025/06/29-20:07:34.764 168c Compacting 1@1 + 1@2 files
2025/06/29-20:07:34.768 168c Generated table #38@1: 456 keys, 12353 bytes
2025/06/29-20:07:34.768 168c Compacted 1@1 + 1@2 files => 12353 bytes
2025/06/29-20:07:34.769 168c compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/29-20:07:34.769 168c Delete type=2 #37
2025/06/29-20:07:34.769 168c Manual compaction at level-1 from '\x00\x10\x00\x00\x05' @ 1845 : 0 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
