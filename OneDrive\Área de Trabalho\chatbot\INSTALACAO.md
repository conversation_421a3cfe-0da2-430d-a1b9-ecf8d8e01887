# 📋 Guia de Instalação - WhatsApp Business Bot

## 🚀 Passo a Passo Completo

### 1. ✅ Verificar Pré-requisitos

Antes de começar, certifique-se de que você tem:
- **Node.js** versão 16 ou superior instalado
- **WhatsApp** instalado no seu celular
- **Conexão estável** com a internet
- **Computador** que ficará ligado para o bot funcionar

### 2. 📦 Instalar Dependências

Abra o terminal/prompt de comando na pasta do projeto e execute:

```bash
npm install
```

Este comando irá instalar todas as bibliotecas necessárias:
- `whatsapp-web.js` - Para conectar com WhatsApp
- `node-cron` - Para mensagens programadas
- `express` - Para o painel web
- `sqlite3` - Para o banco de dados
- E outras dependências

### 3. ⚙️ Configurar o Bot

Edite o arquivo `config/settings.js` para personalizar:

#### 📝 Mensagens do Menu
```javascript
welcomeMessage: `🤖 *Olá! Bem-vindo ao nosso atendimento!*
// Personalize sua mensagem de boas-vindas aqui
```

#### 🕐 Mensagens Programadas
```javascript
scheduledMessages: {
    messages: [
        {
            name: 'Bom dia',
            cron: '0 9 * * 1-5', // Segunda a sexta às 9h
            message: 'Sua mensagem de bom dia aqui',
            active: true
        }
    ]
}
```

#### 📞 Informações de Contato
Atualize telefones, endereços e informações da sua empresa.

### 4. 🚀 Iniciar o Bot

Execute o comando:

```bash
npm start
```

### 5. 📱 Conectar WhatsApp

1. **QR Code aparecerá** no terminal
2. **Abra WhatsApp** no seu celular
3. **Vá em Configurações** > **Aparelhos conectados**
4. **Toque em "Conectar um aparelho"**
5. **Escaneie o QR Code** que apareceu no terminal

⚠️ **IMPORTANTE**: Mantenha seu celular conectado à internet!

### 6. 🌐 Acessar Painel de Administração

Abra seu navegador e acesse:
```
http://localhost:3000
```

## 📊 Funcionalidades do Painel

### Dashboard
- Estatísticas em tempo real
- Mensagens recentes
- Status da conexão

### Clientes
- Lista de todos os clientes
- Histórico de conversas
- Estatísticas por cliente

### Mensagens
- Enviar mensagem individual
- Histórico completo
- Filtros e busca

### Agendador
- Configurar mensagens programadas
- Ativar/desativar agendamentos
- Monitorar execuções

### Broadcast
- Envio em massa
- Lista de destinatários
- Relatório de entrega

### Relatórios
- Relatórios diários
- Relatórios semanais
- Estatísticas detalhadas

## 🔧 Comandos Úteis

### Desenvolvimento (com auto-reload)
```bash
npm run dev
```

### Parar o Bot
Pressione `Ctrl + C` no terminal

### Ver Logs
Os logs ficam salvos na pasta `logs/`

## ⚠️ Solução de Problemas

### Bot não conecta
1. Verifique se o Node.js está instalado
2. Execute `npm install` novamente
3. Certifique-se que o WhatsApp está funcionando no celular

### QR Code não aparece
1. Feche o terminal
2. Delete a pasta `.wwebjs_auth` (se existir)
3. Execute `npm start` novamente

### Mensagens não são enviadas
1. Verifique se o WhatsApp está conectado
2. Confirme se o celular está com internet
3. Veja os logs na pasta `logs/`

### Painel não abre
1. Verifique se apareceu a mensagem "Servidor web iniciado"
2. Tente acessar `http://127.0.0.1:3000`
3. Verifique se a porta 3000 não está sendo usada

## 🔒 Segurança

### ⚠️ NUNCA faça isso:
- Compartilhar o QR Code
- Deixar o computador desprotegido
- Usar em redes públicas sem VPN

### ✅ Recomendações:
- Use firewall ativo
- Mantenha o sistema atualizado
- Faça backup dos dados regularmente
- Use senhas fortes

## 📱 Como os Clientes Usam

1. **Cliente envia mensagem** para seu WhatsApp Business
2. **Bot responde automaticamente** com o menu de opções
3. **Cliente digita número** (1, 2, 3, etc.) para escolher opção
4. **Bot envia resposta** correspondente à opção escolhida
5. **Cliente pode digitar "MENU"** para voltar ao menu principal

### Comandos Especiais para Clientes:
- `MENU` - Volta ao menu principal
- `ORÇAMENTO [descrição]` - Solicita orçamento
- `AGENDAR [detalhes]` - Solicita agendamento

## 🔄 Manutenção

### Backup Diário
O sistema faz limpeza automática de logs antigos, mas recomendamos:
- Backup semanal do banco de dados (`database/chatbot.db`)
- Backup das configurações (`config/settings.js`)

### Monitoramento
- Verifique os logs diariamente
- Monitore o painel de administração
- Acompanhe as estatísticas

## 📞 Suporte

Se tiver problemas:
1. Consulte os logs em `logs/`
2. Verifique este guia novamente
3. Teste com um número diferente

## 🎯 Próximos Passos

Após o bot estar funcionando:
1. Personalize as mensagens para seu negócio
2. Configure horários ideais para mensagens programadas
3. Teste todas as funcionalidades
4. Treine sua equipe para usar o painel
5. Configure backup automático

---

**✅ Pronto! Seu bot está funcionando!**

Agora seus clientes podem interagir 24/7 com seu WhatsApp Business de forma automatizada! 🚀
