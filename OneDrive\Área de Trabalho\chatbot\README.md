# 🤖 WhatsApp Business Bot

Bot de mensagens programadas para WhatsApp Business com sistema de menus interativos para clientes.

## 📋 Funcionalidades

- ✅ Mensagens programadas automáticas
- ✅ Menus interativos para clientes
- ✅ Sistema de opções personalizáveis
- ✅ Banco de dados para armazenar clientes
- ✅ Painel de administração web
- ✅ Logs e monitoramento
- ✅ Agendamento de mensagens

## 🚀 Instalação

### Pré-requisitos
- Node.js (versão 16 ou superior)
- WhatsApp Web ativo no seu celular

### Passos para instalação

1. **Clone ou baixe o projeto**
2. **Instale as dependências:**
   ```bash
   npm install
   ```

3. **Configure o bot:**
   - Edite o arquivo `config/settings.js`
   - Defina suas mensagens e menus

4. **Execute o bot:**
   ```bash
   npm start
   ```

5. **Escaneie o QR Code** que aparecerá no terminal com seu WhatsApp

## 📁 Estrutura do Projeto

```
chatbot/
├── src/
│   ├── index.js              # Arquivo principal
│   ├── bot/
│   │   ├── whatsapp.js       # Conexão WhatsApp
│   │   ├── messageHandler.js # Processamento de mensagens
│   │   └── menuSystem.js     # Sistema de menus
│   ├── scheduler/
│   │   └── cronJobs.js       # Mensagens programadas
│   ├── database/
│   │   ├── db.js             # Conexão banco de dados
│   │   └── models.js         # Modelos de dados
│   └── web/
│       ├── server.js         # Servidor web
│       └── routes/           # Rotas da API
├── config/
│   └── settings.js           # Configurações
├── public/                   # Interface web
└── logs/                     # Arquivos de log
```

## 🔧 Configuração

Edite o arquivo `config/settings.js` para personalizar:
- Mensagens de boas-vindas
- Opções do menu
- Horários de mensagens programadas
- Configurações do banco de dados

## 📱 Como Usar

1. **Clientes enviam mensagem** para seu WhatsApp Business
2. **Bot responde automaticamente** com menu de opções
3. **Cliente seleciona opção** digitando número correspondente
4. **Bot processa** e responde conforme configurado
5. **Mensagens programadas** são enviadas automaticamente

## 🛠️ Desenvolvimento

Para desenvolvimento com auto-reload:
```bash
npm run dev
```

## 📊 Painel de Administração

Acesse `http://localhost:3000` para:
- Ver estatísticas de mensagens
- Gerenciar clientes
- Configurar mensagens programadas
- Visualizar logs

## 🔒 Segurança

- Mantenha seu QR Code seguro
- Não compartilhe arquivos de sessão
- Use HTTPS em produção
- Configure firewall adequadamente

## 📞 Suporte

Para dúvidas ou problemas, consulte os logs em `logs/` ou verifique a documentação.
