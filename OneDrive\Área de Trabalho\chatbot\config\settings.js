module.exports = {
    // Configurações do bot
    bot: {
        name: 'Bot WhatsApp Business',
        welcomeMessage: `🤖 *Olá! Bem-vindo ao nosso atendimento automatizado!*

Escolha uma das opções abaixo digitando o *número* correspondente:

*1* - 📋 Informações sobre produtos/serviços
*2* - 💰 Preços e orçamentos  
*3* - 📅 Agendar atendimento
*4* - 📞 Falar com atendente
*5* - 🕐 Horário de funcionamento
*6* - 📍 Localização

Digite o número da opção desejada! 👆`,

        // Respostas para cada opção do menu
        menuOptions: {
            '1': {
                title: '📋 Informações sobre produtos/serviços',
                message: `*Nossos Produtos e Serviços:*

🔹 Produto A - Descrição detalhada
🔹 Produto B - Descrição detalhada  
🔹 Serviço C - Descrição detalhada

Para mais informações específicas, digite:
*PRODUTO A*, *PRODUTO B* ou *SERVIÇO C*

Ou digite *MENU* para voltar ao menu principal.`
            },
            '2': {
                title: '💰 Preços e orçamentos',
                message: `*Tabela de Preços:*

💵 *Produto A* - R$ 100,00
💵 *Produto B* - R$ 200,00  
💵 *Serviço C* - R$ 150,00

📝 Para orçamento personalizado, digite *ORÇAMENTO* seguido da sua necessidade.

Exemplo: *ORÇAMENTO preciso de 10 unidades do produto A*

Digite *MENU* para voltar ao menu principal.`
            },
            '3': {
                title: '📅 Agendar atendimento',
                message: `*Agendamento de Atendimento:*

Para agendar, envie uma mensagem com:
*AGENDAR* seguido de:
- Data desejada
- Horário preferido  
- Tipo de atendimento

Exemplo: *AGENDAR 15/12/2024 14:00 Consultoria*

📞 Ou ligue: (11) 99999-9999

Digite *MENU* para voltar ao menu principal.`
            },
            '4': {
                title: '📞 Falar com atendente',
                message: `*Atendimento Humano:*

🕐 *Horário de atendimento:*
Segunda a Sexta: 8h às 18h
Sábado: 8h às 12h

📱 *Contatos diretos:*
WhatsApp: (11) 99999-9999
Telefone: (11) 3333-3333
Email: <EMAIL>

⏰ Fora do horário comercial, deixe sua mensagem que retornaremos em breve!

Digite *MENU* para voltar ao menu principal.`
            },
            '5': {
                title: '🕐 Horário de funcionamento',
                message: `*Horários de Funcionamento:*

🏢 *Loja Física:*
Segunda a Sexta: 8h às 18h
Sábado: 8h às 12h
Domingo: Fechado

💬 *Atendimento Online:*
Segunda a Sexta: 8h às 18h
Sábado: 8h às 12h

🤖 *Bot Automático:*
24 horas por dia, 7 dias por semana

Digite *MENU* para voltar ao menu principal.`
            },
            '6': {
                title: '📍 Localização',
                message: `*Nossa Localização:*

📍 *Endereço:*
Rua Exemplo, 123 - Centro
São Paulo - SP
CEP: 01234-567

🚗 *Como chegar:*
- Próximo ao metrô Sé
- Estacionamento disponível
- Acesso para cadeirantes

🗺️ *Google Maps:*
https://maps.google.com/

📞 *Telefone:* (11) 3333-3333

Digite *MENU* para voltar ao menu principal.`
            }
        },

        // Mensagens de erro
        invalidOption: `❌ Opção inválida! 

Por favor, digite apenas o *número* de uma das opções disponíveis (1 a 6).

Digite *MENU* para ver as opções novamente.`,

        // Palavras-chave especiais
        keywords: {
            'menu': 'SHOW_MENU',
            'oi': 'SHOW_MENU',
            'olá': 'SHOW_MENU',
            'ola': 'SHOW_MENU',
            'início': 'SHOW_MENU',
            'inicio': 'SHOW_MENU'
        }
    },

    // Configurações de mensagens programadas
    scheduledMessages: {
        enabled: true,
        messages: [
            {
                name: 'Bom dia',
                cron: '0 9 * * 1-5', // Segunda a sexta às 9h
                message: '🌅 *Bom dia!* Estamos abertos e prontos para atendê-lo! Como podemos ajudar hoje?',
                active: true
            },
            {
                name: 'Lembrete meio-dia',
                cron: '0 12 * * 1-5', // Segunda a sexta às 12h
                message: '🕐 *Meio-dia!* Não esqueça de conferir nossas promoções especiais. Digite *MENU* para ver as opções!',
                active: true
            },
            {
                name: 'Encerramento',
                cron: '0 18 * * 1-5', // Segunda a sexta às 18h
                message: '🌙 *Encerrando o expediente!* Obrigado por escolher nossos serviços. Até amanhã!',
                active: true
            }
        ]
    },

    // Configurações do banco de dados
    database: {
        type: 'sqlite',
        filename: './database/chatbot.db'
    },

    // Configurações do servidor web
    webServer: {
        port: 3000,
        enabled: true
    },

    // Configurações de logs
    logging: {
        enabled: true,
        level: 'info', // debug, info, warn, error
        directory: './logs'
    }
};
