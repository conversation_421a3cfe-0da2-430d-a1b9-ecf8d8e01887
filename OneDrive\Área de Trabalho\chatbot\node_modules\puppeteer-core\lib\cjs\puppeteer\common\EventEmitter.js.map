{"version": 3, "file": "EventEmitter.js", "sourceRoot": "", "sources": ["../../../../src/common/EventEmitter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAEH,+EAIyC;AA0BzC;;;;;;;;;;;GAWG;AACH,MAAa,YAAY;IAIvB;;OAEG;IACH;QALQ,cAAS,GAAG,IAAI,GAAG,EAAwB,CAAC;QAMlD,IAAI,CAAC,OAAO,GAAG,IAAA,kBAAI,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IACH,EAAE,CAAC,KAAgB,EAAE,OAAgB;QACnC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,GAAG,CAAC,KAAgB,EAAE,OAAgB;QACpC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,KAAgB,EAAE,OAAgB;QAC/C,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,KAAgB,EAAE,OAAgB;QAC5C,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,IAAI,CAAC,KAAgB,EAAE,SAAmB;QACxC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IACH,IAAI,CAAC,KAAgB,EAAE,OAAgB;QACrC,MAAM,WAAW,GAAY,SAAS,CAAC,EAAE;YACvC,OAAO,CAAC,SAAS,CAAC,CAAC;YACnB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAC/B,CAAC,CAAC;QAEF,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;IACrC,CAAC;IAED;;;;;OAKG;IACH,aAAa,CAAC,KAAgB;QAC5B,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;;;;OAKG;IACH,kBAAkB,CAAC,KAAiB;QAClC,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAC9B;aAAM;YACL,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SACxB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,mBAAmB,CAAC,KAAgB;;QAC1C,OAAO,CAAA,MAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,0CAAE,MAAM,KAAI,CAAC,CAAC;IAChD,CAAC;CACF;AA1GD,oCA0GC"}