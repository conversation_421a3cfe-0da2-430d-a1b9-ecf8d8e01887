{"version": 3, "file": "JSHandle.js", "sourceRoot": "", "sources": ["../../../../src/common/JSHandle.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAGH,iDAAyC;AAMzC,uCAA+E;AA8B/E;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAa,QAAQ;IAwBnB;;OAEG;IACH,YACE,OAAyB,EACzB,YAA2C;QAvB7C,6BAAY,KAAK,EAAC;QAClB,oCAA2B;QAC3B,yCAA6C;QAuB3C,uBAAA,IAAI,qBAAY,OAAO,MAAA,CAAC;QACxB,uBAAA,IAAI,0BAAiB,YAAY,MAAA,CAAC;IACpC,CAAC;IAvBD;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,uBAAA,IAAI,yBAAS,CAAC,OAAO,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,uBAAA,IAAI,0BAAU,CAAC;IACxB,CAAC;IAaD;;OAEG;IACH,gBAAgB;QACd,OAAO,uBAAA,IAAI,yBAAS,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAMZ,YAA2B,EAC3B,GAAG,IAAY;QAIf,OAAO,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;IAC7E,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,cAAc,CAMlB,YAA2B,EAC3B,GAAG,IAAY;QAIf,OAAO,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,cAAc,CACjD,YAAY,EACZ,IAAI,EACJ,GAAG,IAAI,CACR,CAAC;IACJ,CAAC;IASD,KAAK,CAAC,WAAW,CACf,YAAyB;QAEzB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE;YAClD,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC;QAC9B,CAAC,EAAE,YAAY,CAAC,CAAC;IACnB,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,KAAK,CAAC,aAAa;QACjB,IAAA,kBAAM,EAAC,uBAAA,IAAI,8BAAc,CAAC,QAAQ,CAAC,CAAC;QACpC,0EAA0E;QAC1E,wDAAwD;QACxD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/D,QAAQ,EAAE,uBAAA,IAAI,8BAAc,CAAC,QAAQ;YACrC,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,EAAoB,CAAC;QAC3C,KAAK,MAAM,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE;YACtC,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;gBAC3C,SAAS;aACV;YACD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAA,wBAAc,EAAC,uBAAA,IAAI,yBAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;SAC1E;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,SAAS;QACb,IAAI,CAAC,uBAAA,IAAI,8BAAc,CAAC,QAAQ,EAAE;YAChC,OAAO,IAAA,+BAAqB,EAAC,uBAAA,IAAI,8BAAc,CAAC,CAAC;SAClD;QACD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACzC,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;QACH,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,SAAS;QACP,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,IAAI,uBAAA,IAAI,0BAAU,EAAE;YAClB,OAAO;SACR;QACD,uBAAA,IAAI,sBAAa,IAAI,MAAA,CAAC;QACtB,MAAM,IAAA,uBAAa,EAAC,IAAI,CAAC,MAAM,EAAE,uBAAA,IAAI,8BAAc,CAAC,CAAC;IACvD,CAAC;IAED;;;;;OAKG;IACH,QAAQ;QACN,IAAI,CAAC,uBAAA,IAAI,8BAAc,CAAC,QAAQ,EAAE;YAChC,OAAO,WAAW,GAAG,IAAA,+BAAqB,EAAC,uBAAA,IAAI,8BAAc,CAAC,CAAC;SAChE;QACD,MAAM,IAAI,GAAG,uBAAA,IAAI,8BAAc,CAAC,OAAO,IAAI,uBAAA,IAAI,8BAAc,CAAC,IAAI,CAAC;QACnE,OAAO,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,YAAY;QACV,OAAO,uBAAA,IAAI,8BAAc,CAAC;IAC5B,CAAC;CACF;AArMD,4BAqMC"}