{"version": 3, "file": "AriaQueryHandler.js", "sourceRoot": "", "sources": ["../../../../src/common/AriaQueryHandler.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AAEzC,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AACjC,OAAO,EAAC,UAAU,EAAE,eAAe,EAAC,MAAM,oBAAoB,CAAC;AAG/D,KAAK,UAAU,WAAW,CACxB,MAAkB,EAClB,OAA4B,EAC5B,cAAuB,EACvB,IAAa;IAEb,MAAM,EAAC,KAAK,EAAC,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;QAC7D,QAAQ,EAAE,OAAO,CAAC,YAAY,EAAE,CAAC,QAAQ;QACzC,cAAc;QACd,IAAI;KACL,CAAC,CAAC;IACH,MAAM,aAAa,GAAoC,KAAK,CAAC,MAAM,CACjE,CAAC,IAAmC,EAAE,EAAE;QACtC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC;IACxD,CAAC,CACF,CAAC;IACF,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;IAC/C,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AAC1C,CAAC,CAAC;AACF,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;AAClD,MAAM,eAAe,GACnB,yFAAyF,CAAC;AAG5F,SAAS,gBAAgB,CACvB,SAAiB;IAEjB,OAAO,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACxC,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAS,iBAAiB,CAAC,QAAgB;IACzC,MAAM,YAAY,GAAoB,EAAE,CAAC;IACzC,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAClC,eAAe,EACf,CAAC,CAAC,EAAE,SAAiB,EAAE,MAAc,EAAE,KAAa,EAAE,EAAE;QACtD,SAAS,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;QAC7B,MAAM,CACJ,gBAAgB,CAAC,SAAS,CAAC,EAC3B,2BAA2B,SAAS,eAAe,CACpD,CAAC;QACF,YAAY,CAAC,SAAS,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;QAChD,OAAO,EAAE,CAAC;IACZ,CAAC,CACF,CAAC;IACF,IAAI,WAAW,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;QACrC,YAAY,CAAC,IAAI,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC;KACjD;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,MAAM,UAAU,GAAG,KAAK,EAAE,OAA4B,EAAE,QAAgB,EAAE,EAAE;IAC1E,MAAM,EAAC,IAAI,EAAE,IAAI,EAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IACjD,MAAM,GAAG,GAAG,MAAM,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACnE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAE;QACvC,OAAO,IAAI,CAAC;KACb;IACD,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;AACjC,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAsC,KAAK,EACvD,OAAO,EACP,QAAQ,EACR,EAAE;IACF,MAAM,EAAE,GAAG,MAAM,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC/C,IAAI,CAAC,EAAE,EAAE;QACP,OAAO,IAAI,CAAC;KACb;IACD,OAAO,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,gBAAgB,CAC7D,EAAE,CACH,CAAwB,CAAC;AAC5B,CAAC,CAAC;AAEF,MAAM,OAAO,GAAqC,KAAK,EACrD,cAAc,EACd,QAAQ,EACR,OAAO,EACP,EAAE;IACF,IAAI,KAAY,CAAC;IACjB,IAAI,OAAwC,CAAC;IAC7C,IAAI,cAAc,YAAY,KAAK,EAAE;QACnC,KAAK,GAAG,cAAc,CAAC;KACxB;SAAM;QACL,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC;QAC7B,OAAO,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;KAC3E;IAED,MAAM,iBAAiB,GAAG,KAAK,EAAE,QAAgB,EAAE,EAAE;QACnD,MAAM,EAAE,GAAG,MAAM,UAAU,CACzB,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,EAAE,CAAC,EAC3D,QAAQ,CACT,CAAC;QACF,IAAI,CAAC,EAAE,EAAE;YACP,OAAO,IAAI,CAAC;SACb;QACD,OAAO,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,gBAAgB,CAC1D,EAAE,CACH,CAAwB,CAAC;IAC5B,CAAC,CAAC;IAEF,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,sBAAsB,CACvE,CAAC,CAAU,EAAE,QAAgB,EAAE,EAAE;QAC/B,OACE,UAGD,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC,EACD,OAAO,EACP,QAAQ,EACR,OAAO,EACP,IAAI,GAAG,CAAC,CAAC,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC,CAAC,CACpD,CAAC;IACF,IAAI,OAAO,EAAE;QACX,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;KACzB;IACD,IAAI,CAAC,CAAC,MAAM,YAAY,aAAa,CAAC,EAAE;QACtC,MAAM,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,EAAE,CAAA,CAAC;QACxB,OAAO,IAAI,CAAC;KACb;IACD,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAChE,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAsC,KAAK,EACvD,OAAO,EACP,QAAQ,EACR,EAAE;IACF,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;IAC1C,MAAM,EAAC,IAAI,EAAE,IAAI,EAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IACjD,MAAM,GAAG,GAAG,MAAM,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACnE,MAAM,KAAK,GAAG,MAAM,CAAC,MAAO,CAAC;IAC7B,OAAO,OAAO,CAAC,GAAG,CAChB,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;QACf,OAAO,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,gBAAgB,CAEpD,CAAC;IACJ,CAAC,CAAC,CACH,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAA0B;IAChD,QAAQ;IACR,OAAO;IACP,QAAQ;CACT,CAAC"}