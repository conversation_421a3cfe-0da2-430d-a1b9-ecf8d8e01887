{"version": 3, "file": "Browser.js", "sourceRoot": "", "sources": ["../../../../src/common/Browser.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;AAIH,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAyB,uBAAuB,EAAC,MAAM,iBAAiB,CAAC;AAChF,OAAO,EAAC,eAAe,EAAC,MAAM,WAAW,CAAC;AAG1C,OAAO,EAAC,MAAM,EAAC,MAAM,aAAa,CAAC;AACnC,OAAO,EAAC,SAAS,EAAC,MAAM,gBAAgB,CAAC;AAEzC,OAAO,EAAC,mBAAmB,EAAC,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAC,oBAAoB,EAAC,MAAM,2BAA2B,CAAC;AAC/D,OAAO,EACL,OAAO,IAAI,WAAW,EACtB,cAAc,EAOd,qCAAqC,GAGtC,MAAM,mBAAmB,CAAC;AAE3B;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,WAAW;IAgDzC;;OAEG;IACH,YACE,OAAyC,EACzC,UAAsB,EACtB,UAAoB,EACpB,iBAA0B,EAC1B,eAAiC,EACjC,OAAsB,EACtB,aAAoC,EACpC,oBAA2C,EAC3C,oBAA2C;QAE3C,KAAK,EAAE,CAAC;;QAjCV,gDAA4B;QAC5B,8CAAmC;QACnC,sCAAwB;QACxB,yCAAwB;QACxB,4CAAqC;QACrC,mDAA4C;QAC5C,mDAA6C;QAC7C,6CAAmC;QACnC,uCAA0C;QAC1C,kDAAgC;QAChC,4CAA8B;QA4D9B,uCAAoB,GAAG,EAAE;YACvB,IAAI,CAAC,IAAI,wDAAmC,CAAC;QAC/C,CAAC,EAAC;QA4JF,mCAAgB,CACd,UAAsC,EACtC,OAAoB,EACpB,EAAE;;YACF,MAAM,EAAC,gBAAgB,EAAC,GAAG,UAAU,CAAC;YACtC,MAAM,OAAO,GACX,gBAAgB,IAAI,uBAAA,IAAI,4BAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC;gBACtD,CAAC,CAAC,uBAAA,IAAI,4BAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC;gBACtC,CAAC,CAAC,uBAAA,IAAI,kCAAgB,CAAC;YAE3B,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;aAC5C;YAED,OAAO,IAAI,MAAM,CACf,UAAU,EACV,OAAO,EACP,OAAO,EACP,uBAAA,IAAI,iCAAe,EACnB,CAAC,oBAA6B,EAAE,EAAE;gBAChC,OAAO,uBAAA,IAAI,8BAAY,CAAC,cAAc,CACpC,UAAU,EACV,oBAAoB,CACrB,CAAC;YACJ,CAAC,EACD,uBAAA,IAAI,qCAAmB,EACvB,MAAA,uBAAA,IAAI,mCAAiB,mCAAI,IAAI,EAC7B,uBAAA,IAAI,uCAAqB,EACzB,uBAAA,IAAI,wCAAsB,CAC3B,CAAC;QACJ,CAAC,EAAC;QAEF,yCAAsB,KAAK,EAAE,MAAc,EAAE,EAAE;YAC7C,IAAI,MAAM,MAAM,CAAC,mBAAmB,EAAE;gBACpC,IAAI,CAAC,IAAI,2DAAqC,MAAM,CAAC,CAAC;gBACtD,MAAM;qBACH,cAAc,EAAE;qBAChB,IAAI,kEAA4C,MAAM,CAAC,CAAC;aAC5D;QACH,CAAC,EAAC;QAEF,2CAAwB,KAAK,EAAE,MAAc,EAAiB,EAAE;YAC9D,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,MAAM,MAAM,CAAC,mBAAmB,EAAE;gBACpC,IAAI,CAAC,IAAI,+DAAuC,MAAM,CAAC,CAAC;gBACxD,MAAM;qBACH,cAAc,EAAE;qBAChB,IAAI,sEAA8C,MAAM,CAAC,CAAC;aAC9D;QACH,CAAC,EAAC;QAEF,sCAAmB,CAAC,EAClB,MAAM,EACN,UAAU,GAIX,EAAQ,EAAE;YACT,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;YACjC,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;YAC7C,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACtC,IAAI,cAAc,IAAI,WAAW,KAAK,MAAM,CAAC,GAAG,EAAE,EAAE;gBAClD,IAAI,CAAC,IAAI,2DAAqC,MAAM,CAAC,CAAC;gBACtD,MAAM;qBACH,cAAc,EAAE;qBAChB,IAAI,kEAA4C,MAAM,CAAC,CAAC;aAC5D;QACH,CAAC,EAAC;QAEF,yCAAsB,CAAC,UAAsC,EAAQ,EAAE;YACrE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QAC5C,CAAC,EAAC;QA1QA,OAAO,GAAG,OAAO,IAAI,QAAQ,CAAC;QAC9B,uBAAA,IAAI,iCAAsB,iBAAiB,MAAA,CAAC;QAC5C,uBAAA,IAAI,+BAAoB,eAAe,MAAA,CAAC;QACxC,uBAAA,IAAI,uBAAY,OAAO,MAAA,CAAC;QACxB,uBAAA,IAAI,mCAAwB,IAAI,SAAS,EAAE,MAAA,CAAC;QAC5C,uBAAA,IAAI,0BAAe,UAAU,MAAA,CAAC;QAC9B,uBAAA,IAAI,6BAAkB,aAAa,IAAI,cAAmB,CAAC,MAAA,CAAC;QAC5D,uBAAA,IAAI,oCACF,oBAAoB;YACpB,CAAC,GAAY,EAAE;gBACb,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,MAAA,CAAC;QACL,uBAAA,IAAI,kEAAyB,MAA7B,IAAI,EAA0B,oBAAoB,CAAC,CAAC;QACpD,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,uBAAA,IAAI,6BAAkB,IAAI,oBAAoB,CAC5C,UAAU,EACV,uBAAA,IAAI,gCAAc,EAClB,uBAAA,IAAI,wCAAsB,CAC3B,MAAA,CAAC;SACH;aAAM;YACL,uBAAA,IAAI,6BAAkB,IAAI,mBAAmB,CAC3C,UAAU,EACV,uBAAA,IAAI,gCAAc,EAClB,uBAAA,IAAI,wCAAsB,CAC3B,MAAA,CAAC;SACH;QACD,uBAAA,IAAI,8BAAmB,IAAI,iBAAiB,CAAC,uBAAA,IAAI,8BAAY,EAAE,IAAI,CAAC,MAAA,CAAC;QACrE,uBAAA,IAAI,wBAAa,IAAI,GAAG,EAAE,MAAA,CAAC;QAC3B,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAClC,uBAAA,IAAI,4BAAU,CAAC,GAAG,CAChB,SAAS,EACT,IAAI,iBAAiB,CAAC,uBAAA,IAAI,8BAAY,EAAE,IAAI,EAAE,SAAS,CAAC,CACzD,CAAC;SACH;IACH,CAAC;IAhGD;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,OAAO,CAClB,OAAyC,EACzC,UAAsB,EACtB,UAAoB,EACpB,iBAA0B,EAC1B,eAAiC,EACjC,OAAsB,EACtB,aAAoC,EACpC,oBAA2C,EAC3C,oBAA2C;QAE3C,MAAM,OAAO,GAAG,IAAI,UAAU,CAC5B,OAAO,EACP,UAAU,EACV,UAAU,EACV,iBAAiB,EACjB,eAAe,EACf,OAAO,EACP,aAAa,EACb,oBAAoB,EACpB,oBAAoB,CACrB,CAAC;QACF,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;QACxB,OAAO,OAAO,CAAC;IACjB,CAAC;IAaD;;OAEG;IACH,IAAa,QAAQ;QACnB,OAAO,uBAAA,IAAI,iCAAe,CAAC,mBAAmB,EAAE,CAAC;IACnD,CAAC;IAyDD;;OAEG;IACM,KAAK,CAAC,OAAO;QACpB,uBAAA,IAAI,8BAAY,CAAC,EAAE,CACjB,uBAAuB,CAAC,YAAY,EACpC,uBAAA,IAAI,oCAAkB,CACvB,CAAC;QACF,uBAAA,IAAI,iCAAe,CAAC,EAAE,qEAEpB,uBAAA,IAAI,sCAAoB,CACzB,CAAC;QACF,uBAAA,IAAI,iCAAe,CAAC,EAAE,2DAEpB,uBAAA,IAAI,wCAAsB,CAC3B,CAAC;QACF,uBAAA,IAAI,iCAAe,CAAC,EAAE,iEAEpB,uBAAA,IAAI,mCAAiB,CACtB,CAAC;QACF,uBAAA,IAAI,iCAAe,CAAC,EAAE,uEAEpB,uBAAA,IAAI,sCAAoB,CACzB,CAAC;QACF,MAAM,uBAAA,IAAI,iCAAe,CAAC,UAAU,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACM,OAAO;QACd,uBAAA,IAAI,8BAAY,CAAC,GAAG,CAClB,uBAAuB,CAAC,YAAY,EACpC,uBAAA,IAAI,oCAAkB,CACvB,CAAC;QACF,uBAAA,IAAI,iCAAe,CAAC,GAAG,qEAErB,uBAAA,IAAI,sCAAoB,CACzB,CAAC;QACF,uBAAA,IAAI,iCAAe,CAAC,GAAG,2DAErB,uBAAA,IAAI,wCAAsB,CAC3B,CAAC;QACF,uBAAA,IAAI,iCAAe,CAAC,GAAG,iEAErB,uBAAA,IAAI,mCAAiB,CACtB,CAAC;QACF,uBAAA,IAAI,iCAAe,CAAC,GAAG,uEAErB,uBAAA,IAAI,sCAAoB,CACzB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACM,OAAO;;QACd,OAAO,MAAA,uBAAA,IAAI,2BAAS,mCAAI,IAAI,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,uBAAA,IAAI,iCAAe,CAAC;IAC7B,CAAC;IAcD;;OAEG;IACM,wBAAwB;QAC/B,OAAO,uBAAA,IAAI,wCAAsB,CAAC;IACpC,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACM,KAAK,CAAC,6BAA6B,CAC1C,UAAiC,EAAE;QAEnC,MAAM,EAAC,WAAW,EAAE,eAAe,EAAC,GAAG,OAAO,CAAC;QAE/C,MAAM,EAAC,gBAAgB,EAAC,GAAG,MAAM,uBAAA,IAAI,8BAAY,CAAC,IAAI,CACpD,6BAA6B,EAC7B;YACE,WAAW;YACX,eAAe,EAAE,eAAe,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC;SAC9D,CACF,CAAC;QACF,MAAM,OAAO,GAAG,IAAI,iBAAiB,CACnC,uBAAA,IAAI,8BAAY,EAChB,IAAI,EACJ,gBAAgB,CACjB,CAAC;QACF,uBAAA,IAAI,4BAAU,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAC9C,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACM,eAAe;QACtB,OAAO,CAAC,uBAAA,IAAI,kCAAgB,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,uBAAA,IAAI,4BAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACM,qBAAqB;QAC5B,OAAO,uBAAA,IAAI,kCAAgB,CAAC;IAC9B,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,eAAe,CAAC,SAAkB;QAC/C,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;SACR;QACD,MAAM,uBAAA,IAAI,8BAAY,CAAC,IAAI,CAAC,8BAA8B,EAAE;YAC1D,gBAAgB,EAAE,SAAS;SAC5B,CAAC,CAAC;QACH,uBAAA,IAAI,4BAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IA4ED;;;;;;;;;;;;;;;;OAgBG;IACM,UAAU;QACjB,OAAO,uBAAA,IAAI,8BAAY,CAAC,GAAG,EAAE,CAAC;IAChC,CAAC;IAED;;;OAGG;IACM,KAAK,CAAC,OAAO;QACpB,OAAO,uBAAA,IAAI,kCAAgB,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,oBAAoB,CAAC,SAAkB;QACpD,MAAM,EAAC,QAAQ,EAAC,GAAG,MAAM,uBAAA,IAAI,8BAAY,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACpE,GAAG,EAAE,aAAa;YAClB,gBAAgB,EAAE,SAAS,IAAI,SAAS;SACzC,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,uBAAA,IAAI,iCAAe,CAAC,mBAAmB,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvE,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,iCAAiC,QAAQ,GAAG,CAAC,CAAC;SAC/D;QACD,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC;QACrD,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,0CAA0C,QAAQ,GAAG,CAAC,CAAC;SACxE;QACD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CACb,6CAA6C,SAAS,GAAG,CAC1D,CAAC;SACH;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACM,OAAO;QACd,OAAO,KAAK,CAAC,IAAI,CACf,uBAAA,IAAI,iCAAe,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,CACnD,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAChB,OAAO,MAAM,CAAC,cAAc,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACM,MAAM;QACb,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACjD,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACM,KAAK,CAAC,aAAa,CAC1B,SAAoD,EACpD,UAAgC,EAAE;QAElC,MAAM,EAAC,OAAO,GAAG,KAAK,EAAC,GAAG,OAAO,CAAC;QAClC,IAAI,OAAsD,CAAC;QAC3D,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,MAAM,aAAa,GAAG,IAAI,OAAO,CAAS,CAAC,CAAC,EAAE;YAC5C,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,EAAE,2DAAqC,KAAK,CAAC,CAAC;QACnD,IAAI,CAAC,EAAE,2DAAqC,KAAK,CAAC,CAAC;QACnD,IAAI;YACF,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC9B,IAAI,CAAC,OAAO,EAAE;gBACZ,OAAO,MAAM,aAAa,CAAC;aAC5B;YACD,OAAO,MAAM,eAAe,CAAC,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;SAChE;gBAAS;YACR,IAAI,CAAC,GAAG,2DAAqC,KAAK,CAAC,CAAC;YACpD,IAAI,CAAC,GAAG,2DAAqC,KAAK,CAAC,CAAC;SACrD;QAED,KAAK,UAAU,KAAK,CAAC,MAAc;YACjC,IAAI,CAAC,MAAM,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE;gBAC5C,UAAU,GAAG,IAAI,CAAC;gBAClB,OAAO,CAAC,MAAM,CAAC,CAAC;aACjB;QACH,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACM,KAAK,CAAC,KAAK;QAClB,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CACpC,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACnC,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC,CAAC,CACH,CAAC;QACF,iBAAiB;QACjB,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;YACpC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED;;;;;;;;;OASG;IACM,KAAK,CAAC,OAAO;QACpB,MAAM,OAAO,GAAG,MAAM,uBAAA,IAAI,qDAAY,MAAhB,IAAI,CAAc,CAAC;QACzC,OAAO,OAAO,CAAC,OAAO,CAAC;IACzB,CAAC;IAED;;;OAGG;IACM,KAAK,CAAC,SAAS;QACtB,MAAM,OAAO,GAAG,MAAM,uBAAA,IAAI,qDAAY,MAAhB,IAAI,CAAc,CAAC;QACzC,OAAO,OAAO,CAAC,SAAS,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACM,KAAK,CAAC,KAAK;QAClB,MAAM,uBAAA,IAAI,iCAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACM,UAAU;QACjB,uBAAA,IAAI,iCAAe,CAAC,OAAO,EAAE,CAAC;QAC9B,uBAAA,IAAI,8BAAY,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACM,WAAW;QAClB,OAAO,CAAC,uBAAA,IAAI,8BAAY,CAAC,OAAO,CAAC;IACnC,CAAC;CAKF;u3BA1W0B,oBAA2C;IAClE,uBAAA,IAAI,oCACF,oBAAoB;QACpB,CAAC,CAAC,MAAkC,EAAW,EAAE;YAC/C,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,MAAM;gBACtB,MAAM,CAAC,IAAI,KAAK,iBAAiB;gBACjC,MAAM,CAAC,IAAI,KAAK,SAAS,CAC1B,CAAC;QACJ,CAAC,CAAC,MAAA,CAAC;AACP,CAAC;IA8VC,OAAO,uBAAA,IAAI,8BAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;AACrD,CAAC;AAGH;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,cAAc;IAKnD;;OAEG;IACH,YAAY,UAAsB,EAAE,OAAmB,EAAE,SAAkB;QACzE,KAAK,EAAE,CAAC;QARV,gDAAwB;QACxB,6CAAqB;QACrB,wCAAa;QAOX,uBAAA,IAAI,iCAAe,UAAU,MAAA,CAAC;QAC9B,uBAAA,IAAI,8BAAY,OAAO,MAAA,CAAC;QACxB,uBAAA,IAAI,yBAAO,SAAS,MAAA,CAAC;IACvB,CAAC;IAED;;OAEG;IACM,OAAO;QACd,OAAO,uBAAA,IAAI,kCAAS,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7C,OAAO,MAAM,CAAC,cAAc,EAAE,KAAK,IAAI,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACM,aAAa,CACpB,SAAoD,EACpD,UAA8B,EAAE;QAEhC,OAAO,uBAAA,IAAI,kCAAS,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;YAC1C,OAAO,MAAM,CAAC,cAAc,EAAE,KAAK,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;QAC/D,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACM,KAAK,CAAC,KAAK;QAClB,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAC7B,IAAI,CAAC,OAAO,EAAE;aACX,MAAM,CAAC,MAAM,CAAC,EAAE;;YACf,OAAO,CACL,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM;gBACxB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,OAAO;qBACxB,MAAA,uBAAA,IAAI,kCAAS,CAAC,wBAAwB,EAAE,0CACtC,MAAM,CAAC,cAAc,EAAE,CACxB,CAAA,CAAC,CACL,CAAC;QACJ,CAAC,CAAC;aACD,GAAG,CAAC,MAAM,CAAC,EAAE;YACZ,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;QACvB,CAAC,CAAC,CACL,CAAC;QACF,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAgB,EAAE;YACzC,OAAO,CAAC,CAAC,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACM,WAAW;QAClB,OAAO,CAAC,CAAC,uBAAA,IAAI,6BAAI,CAAC;IACpB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACM,KAAK,CAAC,mBAAmB,CAChC,MAAc,EACd,WAAyB;QAEzB,MAAM,mBAAmB,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACvD,MAAM,kBAAkB,GACtB,qCAAqC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACxD,IAAI,CAAC,kBAAkB,EAAE;gBACvB,MAAM,IAAI,KAAK,CAAC,sBAAsB,GAAG,UAAU,CAAC,CAAC;aACtD;YACD,OAAO,kBAAkB,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,MAAM,uBAAA,IAAI,qCAAY,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtD,MAAM;YACN,gBAAgB,EAAE,uBAAA,IAAI,6BAAI,IAAI,SAAS;YACvC,WAAW,EAAE,mBAAmB;SACjC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACM,KAAK,CAAC,wBAAwB;QACrC,MAAM,uBAAA,IAAI,qCAAY,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtD,gBAAgB,EAAE,uBAAA,IAAI,6BAAI,IAAI,SAAS;SACxC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACM,OAAO;QACd,OAAO,uBAAA,IAAI,kCAAS,CAAC,oBAAoB,CAAC,uBAAA,IAAI,6BAAI,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACM,OAAO;QACd,OAAO,uBAAA,IAAI,kCAAS,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACM,KAAK,CAAC,KAAK;QAClB,MAAM,CAAC,uBAAA,IAAI,6BAAI,EAAE,0CAA0C,CAAC,CAAC;QAC7D,MAAM,uBAAA,IAAI,kCAAS,CAAC,eAAe,CAAC,uBAAA,IAAI,6BAAI,CAAC,CAAC;IAChD,CAAC;CACF"}