{"version": 3, "file": "BrowserConnector.js", "sourceRoot": "", "sources": ["../../../../src/common/BrowserConnector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAC,UAAU,EAAC,MAAM,WAAW,CAAC;AACrC,OAAO,EAAC,WAAW,EAAC,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AAEzC,OAAO,EAAC,UAAU,EAAC,MAAM,cAAc,CAAC;AACxC,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAE3C,OAAO,EAAC,QAAQ,EAAC,MAAM,YAAY,CAAC;AAqCpC,MAAM,0BAA0B,GAAG,KAAK,IAAI,EAAE;IAC5C,OAAO,MAAM;QACX,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAAC,sBAAsB;QACtE,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC;aAC7C,yBAAyB,CAAC;AACnC,CAAC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CACxC,OAIC;IAED,MAAM,EACJ,iBAAiB,EACjB,UAAU,EACV,iBAAiB,GAAG,KAAK,EACzB,eAAe,GAAG,EAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAC,EAC3C,SAAS,EACT,MAAM,GAAG,CAAC,EACV,YAAY,EACZ,aAAa,EAAE,YAAY,GAC5B,GAAG,OAAO,CAAC;IAEZ,MAAM,CACJ,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;QACtE,CAAC,EACH,+FAA+F,CAChG,CAAC;IAEF,IAAI,UAAuB,CAAC;IAC5B,IAAI,SAAS,EAAE;QACb,UAAU,GAAG,IAAI,UAAU,CAAC,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;KACpD;SAAM,IAAI,iBAAiB,EAAE;QAC5B,MAAM,cAAc,GAAG,MAAM,0BAA0B,EAAE,CAAC;QAC1D,MAAM,mBAAmB,GACvB,MAAM,cAAc,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACjD,UAAU,GAAG,IAAI,UAAU,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;KAC7E;SAAM,IAAI,UAAU,EAAE;QACrB,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,UAAU,CAAC,CAAC;QACtD,MAAM,cAAc,GAAG,MAAM,0BAA0B,EAAE,CAAC;QAC1D,MAAM,mBAAmB,GACvB,MAAM,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAC7C,UAAU,GAAG,IAAI,UAAU,CAAC,aAAa,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;KACzE;IACD,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAE5D,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;QAC/D,CAAC,CAAC,SAAS;QACX,CAAC,CAAC,QAAQ,CAAC;IAEb,MAAM,EAAC,iBAAiB,EAAC,GAAG,MAAM,UAAU,CAAC,IAAI,CAC/C,2BAA2B,CAC5B,CAAC;IACF,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,OAAO,CACtC,OAAO,IAAI,QAAQ,EACnB,UAAU,EACV,iBAAiB,EACjB,iBAAiB,EACjB,eAAe,EACf,SAAS,EACT,GAAG,EAAE;QACH,OAAO,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC5D,CAAC,EACD,YAAY,EACZ,YAAY,CACb,CAAC;IACF,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,KAAK,UAAU,aAAa,CAAC,UAAkB;IAC7C,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;IAEzD,MAAM,KAAK,GAAG,MAAM,QAAQ,EAAE,CAAC;IAC/B,IAAI;QACF,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE;YACjD,MAAM,EAAE,KAAK;SACd,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,QAAQ,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;SAC9C;QACD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,oBAAoB,CAAC;KAClC;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;YACtB,KAAK,CAAC,OAAO;gBACX,8CAA8C,WAAW,IAAI;oBAC7D,KAAK,CAAC,OAAO,CAAC;SACjB;QACD,MAAM,KAAK,CAAC;KACb;AACH,CAAC"}