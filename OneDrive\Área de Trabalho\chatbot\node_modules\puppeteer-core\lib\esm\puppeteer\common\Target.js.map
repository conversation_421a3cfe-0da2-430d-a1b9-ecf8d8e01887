{"version": 3, "file": "Target.js", "sourceRoot": "", "sources": ["../../../../src/common/Target.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;AAGH,OAAO,EAAC,SAAS,EAAC,MAAM,gBAAgB,CAAC;AAWzC,OAAO,EAAC,OAAO,EAAC,MAAM,WAAW,CAAC;AAElC;;GAEG;AACH,MAAM,OAAO,MAAM;IA0CjB;;OAEG;IACH,YACE,UAAsC,EACtC,OAA+B,EAC/B,cAA8B,EAC9B,aAA4B,EAC5B,cAAsE,EACtE,iBAA0B,EAC1B,eAAgC,EAChC,mBAA8B,EAC9B,oBAA0C;QArD5C,yCAAgC;QAChC,kCAAsB;QACtB,qCAAwC;QACxC,yCAAwE;QACxE,4CAA4B;QAC5B,0CAA4B;QAC5B,sCAA6B;QAC7B,wCAAoC;QACpC,8CAAgC;QA+BhC,wCAA8B;QAgB5B,uBAAA,IAAI,mBAAY,OAAO,MAAA,CAAC;QACxB,uBAAA,IAAI,yBAAkB,aAAa,MAAA,CAAC;QACpC,uBAAA,IAAI,sBAAe,UAAU,MAAA,CAAC;QAC9B,uBAAA,IAAI,0BAAmB,cAAc,MAAA,CAAC;QACtC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC;QACrC,uBAAA,IAAI,0BAAmB,cAAc,MAAA,CAAC;QACtC,uBAAA,IAAI,6BAAsB,iBAAiB,MAAA,CAAC;QAC5C,uBAAA,IAAI,2BAAoB,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,SAAS,MAAA,CAAC;QACrD,uBAAA,IAAI,+BAAwB,mBAAmB,MAAA,CAAC;QAChD,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,mBAAmB,GAAG,IAAI,OAAO,CAAU,OAAO,CAAC,EAAE;YACxD,OAAO,CAAC,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAC,OAAO,EAAC,EAAE;YACtB,IAAI,CAAC,OAAO,EAAE;gBACZ,OAAO,KAAK,CAAC;aACd;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAA,MAAM,2BAAa,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE;gBAC7D,OAAO,IAAI,CAAC;aACb;YACD,MAAM,UAAU,GAAG,MAAM,uBAAA,MAAM,2BAAa,CAAC;YAC7C,IAAI,CAAC,UAAU,CAAC,aAAa,uCAAyB,EAAE;gBACtD,OAAO,IAAI,CAAC;aACb;YACD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YACpC,UAAU,CAAC,IAAI,wCAA0B,SAAS,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,OAAO,CAAO,OAAO,CAAC,EAAE;YAClD,OAAO,CAAC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,cAAc;YACjB,CAAC,IAAI,CAAC,qBAAqB,CAAC,uBAAA,IAAI,0BAAY,CAAC;gBAC7C,uBAAA,IAAI,0BAAY,CAAC,GAAG,KAAK,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;SACjC;IACH,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,uBAAA,IAAI,uBAAS,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,uBAAA,IAAI,8BAAgB,MAApB,IAAI,EAAiB,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,uBAAA,IAAI,6BAAe,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,uBAAA,IAAI,0BAAY,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;;QACR,IAAI,IAAI,CAAC,qBAAqB,CAAC,uBAAA,IAAI,0BAAY,CAAC,IAAI,CAAC,uBAAA,IAAI,2BAAa,EAAE;YACtE,uBAAA,IAAI,uBAAgB,CAClB,uBAAA,IAAI,uBAAS;gBACX,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,uBAAA,IAAI,uBAAS,CAAC;gBAChC,CAAC,CAAC,uBAAA,IAAI,8BAAgB,MAApB,IAAI,EAAiB,IAAI,CAAC,CAC/B,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;;gBACd,OAAO,OAAO,CAAC,OAAO,CACpB,MAAM,EACN,IAAI,EACJ,uBAAA,IAAI,iCAAmB,EACvB,MAAA,uBAAA,IAAI,+BAAiB,mCAAI,IAAI,EAC7B,uBAAA,IAAI,mCAAqB,CAC1B,CAAC;YACJ,CAAC,CAAC,MAAA,CAAC;SACJ;QACD,OAAO,MAAA,CAAC,MAAM,uBAAA,IAAI,2BAAa,CAAC,mCAAI,IAAI,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACV,IACE,uBAAA,IAAI,0BAAY,CAAC,IAAI,KAAK,gBAAgB;YAC1C,uBAAA,IAAI,0BAAY,CAAC,IAAI,KAAK,eAAe,EACzC;YACA,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,uBAAA,IAAI,6BAAe,EAAE;YACxB,yDAAyD;YACzD,uBAAA,IAAI,yBAAkB,CACpB,uBAAA,IAAI,uBAAS;gBACX,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,uBAAA,IAAI,uBAAS,CAAC;gBAChC,CAAC,CAAC,uBAAA,IAAI,8BAAgB,MAApB,IAAI,EAAiB,KAAK,CAAC,CAChC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACd,OAAO,IAAI,SAAS,CAClB,MAAM,EACN,uBAAA,IAAI,0BAAY,CAAC,GAAG,EACpB,GAAG,EAAE,GAAE,CAAC,CAAC,sBAAsB,EAC/B,GAAG,EAAE,GAAE,CAAC,CAAC,qBAAqB,CAC/B,CAAC;YACJ,CAAC,CAAC,MAAA,CAAC;SACJ;QACD,OAAO,uBAAA,IAAI,6BAAe,CAAC;IAC7B,CAAC;IAED,GAAG;QACD,OAAO,uBAAA,IAAI,0BAAY,CAAC,GAAG,CAAC;IAC9B,CAAC;IAED;;;;;;OAMG;IACH,IAAI;QAQF,MAAM,IAAI,GAAG,uBAAA,IAAI,0BAAY,CAAC,IAAI,CAAC;QACnC,IACE,IAAI,KAAK,MAAM;YACf,IAAI,KAAK,iBAAiB;YAC1B,IAAI,KAAK,gBAAgB;YACzB,IAAI,KAAK,eAAe;YACxB,IAAI,KAAK,SAAS;YAClB,IAAI,KAAK,SAAS,EAClB;YACA,OAAO,IAAI,CAAC;SACb;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,uBAAA,IAAI,8BAAgB,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,uBAAA,IAAI,8BAAgB,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,EAAC,QAAQ,EAAC,GAAG,uBAAA,IAAI,0BAAY,CAAC;QACpC,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO;SACR;QACD,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAsC;QACvD,uBAAA,IAAI,sBAAe,UAAU,MAAA,CAAC;QAE9B,IACE,CAAC,IAAI,CAAC,cAAc;YACpB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,uBAAA,IAAI,0BAAY,CAAC;gBAC5C,uBAAA,IAAI,0BAAY,CAAC,GAAG,KAAK,EAAE,CAAC,EAC9B;YACA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAChC,OAAO;SACR;IACH,CAAC;CACF"}