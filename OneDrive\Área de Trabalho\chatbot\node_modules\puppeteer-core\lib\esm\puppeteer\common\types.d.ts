/**
 * Copyright 2020 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { JSHandle } from './JSHandle.js';
import { ElementHandle } from './ElementHandle.js';
import { LazyArg } from './LazyArg.js';
/**
 * @public
 */
export declare type Awaitable<T> = T | PromiseLike<T>;
/**
 * @public
 */
export declare type HandleFor<T> = T extends Node ? ElementHandle<T> : JSHandle<T>;
/**
 * @public
 */
export declare type HandleOr<T> = HandleFor<T> | JSHandle<T> | T;
/**
 * @public
 */
export declare type FlattenHandle<T> = T extends HandleOr<infer U> ? U : never;
/**
 * @internal
 */
export declare type FlattenLazyArg<T> = T extends LazyArg<infer U> ? U : T;
/**
 * @internal
 */
export declare type InnerLazyParams<T extends unknown[]> = {
    [K in keyof T]: FlattenLazyArg<T[K]>;
};
/**
 * @public
 */
export declare type InnerParams<T extends unknown[]> = {
    [K in keyof T]: FlattenHandle<T[K]>;
};
/**
 * @public
 */
export declare type EvaluateFunc<T extends unknown[]> = (...params: InnerParams<T>) => Awaitable<unknown>;
/**
 * @public
 */
export declare type NodeFor<Selector extends string> = Selector extends keyof HTMLElementTagNameMap ? HTMLElementTagNameMap[Selector] : Selector extends keyof SVGElementTagNameMap ? SVGElementTagNameMap[Selector] : Element;
//# sourceMappingURL=types.d.ts.map