{"version": 3, "file": "puppeteer-core.js", "sourceRoot": "", "sources": ["../../../src/puppeteer-core.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,cAAc,+BAA+B,CAAC;AAC9C,cAAc,oBAAoB,CAAC;AACnC,cAAc,+BAA+B,CAAC;AAC9C,cAAc,0BAA0B,CAAC;AAEzC,OAAO,EAAC,WAAW,EAAC,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAC,aAAa,EAAC,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAC,mBAAmB,EAAC,MAAM,gBAAgB,CAAC;AAEnD;;GAEG;AACH,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC;IAClC,WAAW,EAAE,mBAAmB,CAAC,WAAW,CAAC;IAC7C,eAAe,EAAE,IAAI;CACtB,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,EACX,OAAO,EACP,oBAAoB,EACpB,WAAW,EACX,cAAc,EACd,MAAM,GACP,GAAG,SAAS,CAAC;AAEd,eAAe,SAAS,CAAC"}