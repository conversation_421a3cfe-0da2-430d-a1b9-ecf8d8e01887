<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Business Bot - Painel de Administração</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .card-stat {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .card-stat-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }
        .card-stat-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .card-stat-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .message-received { background-color: #f8f9fa; }
        .message-sent { background-color: #e3f2fd; }
        .navbar-brand { font-weight: bold; }
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .nav-link.active {
            background-color: #007bff;
            color: white !important;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fab fa-whatsapp me-2"></i>
                WhatsApp Business Bot
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <span id="connection-status" class="status-indicator status-offline"></span>
                    <span id="connection-text">Desconectado</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('customers')">
                                <i class="fas fa-users me-2"></i>Clientes
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('messages')">
                                <i class="fas fa-comments me-2"></i>Mensagens
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('scheduler')">
                                <i class="fas fa-clock me-2"></i>Agendador
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('broadcast')">
                                <i class="fas fa-bullhorn me-2"></i>Broadcast
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('reports')">
                                <i class="fas fa-chart-bar me-2"></i>Relatórios
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="section">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Dashboard</h1>
                        <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> Atualizar
                        </button>
                    </div>

                    <!-- Stats Cards -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card card-stat h-100">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-uppercase mb-1">Total de Clientes</div>
                                            <div class="h5 mb-0 font-weight-bold" id="total-customers">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card card-stat-success h-100">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-uppercase mb-1">Total de Mensagens</div>
                                            <div class="h5 mb-0 font-weight-bold" id="total-messages">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-comments fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card card-stat-info h-100">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-uppercase mb-1">Mensagens Hoje</div>
                                            <div class="h5 mb-0 font-weight-bold" id="today-messages">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-calendar-day fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card card-stat-warning h-100">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-uppercase mb-1">Orçamentos Pendentes</div>
                                            <div class="h5 mb-0 font-weight-bold" id="pending-budgets">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-file-invoice-dollar fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Messages -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Mensagens Recentes</h5>
                        </div>
                        <div class="card-body">
                            <div id="recent-messages" class="list-group list-group-flush">
                                <!-- Messages will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customers Section -->
                <div id="customers-section" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Clientes</h1>
                        <button class="btn btn-outline-primary" onclick="loadCustomers()">
                            <i class="fas fa-sync-alt"></i> Atualizar
                        </button>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Telefone</th>
                                            <th>Primeiro Contato</th>
                                            <th>Último Contato</th>
                                            <th>Total Mensagens</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody id="customers-table">
                                        <!-- Customers will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Messages Section -->
                <div id="messages-section" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Mensagens</h1>
                        <div>
                            <button class="btn btn-primary me-2" onclick="showSendMessageModal()">
                                <i class="fas fa-paper-plane"></i> Enviar Mensagem
                            </button>
                            <button class="btn btn-outline-primary" onclick="loadMessages()">
                                <i class="fas fa-sync-alt"></i> Atualizar
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div id="messages-list">
                                <!-- Messages will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Scheduler Section -->
                <div id="scheduler-section" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Mensagens Programadas</h1>
                        <button class="btn btn-primary" onclick="showAddScheduleModal()">
                            <i class="fas fa-plus"></i> Nova Mensagem
                        </button>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Status do Agendador</h5>
                                </div>
                                <div class="card-body">
                                    <div id="scheduler-status">
                                        <!-- Status will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Mensagens Ativas</h5>
                                </div>
                                <div class="card-body">
                                    <div id="active-schedules">
                                        <!-- Active schedules will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Broadcast Section -->
                <div id="broadcast-section" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Envio em Massa</h1>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <form id="broadcast-form">
                                <div class="mb-3">
                                    <label for="broadcast-message" class="form-label">Mensagem</label>
                                    <textarea class="form-control" id="broadcast-message" rows="4" placeholder="Digite sua mensagem aqui..."></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="broadcast-phones" class="form-label">Telefones (um por linha)</label>
                                    <textarea class="form-control" id="broadcast-phones" rows="6" placeholder="5511999999999&#10;5511888888888&#10;..."></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-bullhorn"></i> Enviar para Todos
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Reports Section -->
                <div id="reports-section" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Relatórios</h1>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Relatório Diário</h5>
                                </div>
                                <div class="card-body">
                                    <div id="daily-report">
                                        <!-- Daily report will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Relatório Semanal</h5>
                                </div>
                                <div class="card-body">
                                    <div id="weekly-report">
                                        <!-- Weekly report will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modals -->
    <!-- Send Message Modal -->
    <div class="modal fade" id="sendMessageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Enviar Mensagem</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="send-message-form">
                        <div class="mb-3">
                            <label for="message-phone" class="form-label">Telefone</label>
                            <input type="text" class="form-control" id="message-phone" placeholder="5511999999999">
                        </div>
                        <div class="mb-3">
                            <label for="message-text" class="form-label">Mensagem</label>
                            <textarea class="form-control" id="message-text" rows="4"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="sendMessage()">Enviar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let currentSection = 'dashboard';

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            checkStatus();
            loadDashboard();

            // Auto-refresh every 30 seconds
            setInterval(checkStatus, 30000);
        });

        // Navigation
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.style.display = 'none';
            });

            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionName + '-section').style.display = 'block';

            // Add active class to clicked nav link
            event.target.classList.add('active');

            currentSection = sectionName;

            // Load section data
            switch(sectionName) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'customers':
                    loadCustomers();
                    break;
                case 'messages':
                    loadMessages();
                    break;
                case 'scheduler':
                    loadScheduler();
                    break;
                case 'reports':
                    loadReports();
                    break;
            }
        }

        // API calls
        async function apiCall(endpoint, options = {}) {
            try {
                const response = await fetch('/api' + endpoint, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                const data = await response.json();
                return data;
            } catch (error) {
                console.error('API Error:', error);
                showAlert('Erro na comunicação com o servidor', 'danger');
                return { success: false, error: error.message };
            }
        }

        // Status check
        async function checkStatus() {
            const result = await apiCall('/status');

            if (result.success) {
                const isConnected = result.data.bot.connected;
                const statusIndicator = document.getElementById('connection-status');
                const statusText = document.getElementById('connection-text');

                if (isConnected) {
                    statusIndicator.className = 'status-indicator status-online';
                    statusText.textContent = 'Conectado';
                } else {
                    statusIndicator.className = 'status-indicator status-offline';
                    statusText.textContent = 'Desconectado';
                }
            }
        }

        // Dashboard
        async function loadDashboard() {
            const result = await apiCall('/dashboard');

            if (result.success) {
                const data = result.data;
                document.getElementById('total-customers').textContent = data.totalCustomers || 0;
                document.getElementById('total-messages').textContent = data.totalMessages || 0;
                document.getElementById('today-messages').textContent = data.todayMessages || 0;
                document.getElementById('pending-budgets').textContent = data.pendingBudgets || 0;
            }

            // Load recent messages
            loadRecentMessages();
        }

        async function refreshDashboard() {
            await loadDashboard();
            showAlert('Dashboard atualizado!', 'success');
        }

        async function loadRecentMessages() {
            const result = await apiCall('/messages?limit=10');

            if (result.success) {
                const messagesContainer = document.getElementById('recent-messages');
                messagesContainer.innerHTML = '';

                result.data.forEach(message => {
                    const messageElement = document.createElement('div');
                    messageElement.className = `list-group-item ${message.type === 'received' ? 'message-received' : 'message-sent'}`;
                    messageElement.innerHTML = `
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">${message.name || message.phone}</h6>
                            <small>${formatDate(message.timestamp)}</small>
                        </div>
                        <p class="mb-1">${message.message}</p>
                        <small class="text-muted">${message.type === 'received' ? 'Recebida' : 'Enviada'}</small>
                    `;
                    messagesContainer.appendChild(messageElement);
                });
            }
        }

        // Customers
        async function loadCustomers() {
            const result = await apiCall('/customers');

            if (result.success) {
                const tableBody = document.getElementById('customers-table');
                tableBody.innerHTML = '';

                result.data.forEach(customer => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${customer.name || 'Sem nome'}</td>
                        <td>${customer.phone}</td>
                        <td>${formatDate(customer.first_contact)}</td>
                        <td>${formatDate(customer.last_contact)}</td>
                        <td>${customer.total_messages}</td>
                        <td>
                            <button class="btn btn-sm btn-primary" onclick="viewConversation('${customer.phone}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-success" onclick="sendMessageToCustomer('${customer.phone}')">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            }
        }

        // Messages
        async function loadMessages() {
            const result = await apiCall('/messages?limit=50');

            if (result.success) {
                const messagesContainer = document.getElementById('messages-list');
                messagesContainer.innerHTML = '';

                result.data.forEach(message => {
                    const messageElement = document.createElement('div');
                    messageElement.className = `card mb-2 ${message.type === 'received' ? 'message-received' : 'message-sent'}`;
                    messageElement.innerHTML = `
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between">
                                <strong>${message.name || message.phone}</strong>
                                <small class="text-muted">${formatDate(message.timestamp)}</small>
                            </div>
                            <p class="mb-1">${message.message}</p>
                            <small class="badge ${message.type === 'received' ? 'bg-primary' : 'bg-success'}">
                                ${message.type === 'received' ? 'Recebida' : 'Enviada'}
                            </small>
                        </div>
                    `;
                    messagesContainer.appendChild(messageElement);
                });
            }
        }

        // Scheduler
        async function loadScheduler() {
            const result = await apiCall('/scheduler/status');

            if (result.success) {
                const statusContainer = document.getElementById('scheduler-status');
                const schedulesContainer = document.getElementById('active-schedules');

                statusContainer.innerHTML = `
                    <p><strong>Status:</strong> ${result.data.running ? 'Ativo' : 'Inativo'}</p>
                    <p><strong>Jobs Ativos:</strong> ${result.data.activeJobs}</p>
                `;

                schedulesContainer.innerHTML = '';
                result.data.jobNames.forEach(jobName => {
                    const jobElement = document.createElement('div');
                    jobElement.className = 'alert alert-info d-flex justify-content-between align-items-center';
                    jobElement.innerHTML = `
                        <span>${jobName}</span>
                        <button class="btn btn-sm btn-danger" onclick="removeSchedule('${jobName}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    `;
                    schedulesContainer.appendChild(jobElement);
                });
            }
        }

        // Reports
        async function loadReports() {
            // Load daily report
            const dailyResult = await apiCall('/reports/daily');
            if (dailyResult.success) {
                const dailyContainer = document.getElementById('daily-report');
                const data = dailyResult.data;
                dailyContainer.innerHTML = `
                    <p><strong>Data:</strong> ${data.date}</p>
                    <p><strong>Total de Mensagens:</strong> ${data.total_messages}</p>
                    <p><strong>Mensagens Recebidas:</strong> ${data.received_messages}</p>
                    <p><strong>Mensagens Enviadas:</strong> ${data.sent_messages}</p>
                    <p><strong>Clientes Únicos:</strong> ${data.unique_customers}</p>
                `;
            }

            // Load weekly report
            const weeklyResult = await apiCall('/reports/weekly');
            if (weeklyResult.success) {
                const weeklyContainer = document.getElementById('weekly-report');
                weeklyContainer.innerHTML = '<h6>Últimos 7 dias:</h6>';

                weeklyResult.data.forEach(day => {
                    const dayElement = document.createElement('div');
                    dayElement.className = 'border-bottom py-2';
                    dayElement.innerHTML = `
                        <strong>${day.date}</strong><br>
                        Mensagens: ${day.total_messages} | Clientes: ${day.unique_customers}
                    `;
                    weeklyContainer.appendChild(dayElement);
                });
            }
        }

        // Utility functions
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('pt-BR');
        }

        function showAlert(message, type = 'info') {
            // Create alert element
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alert);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }

        // Modal functions
        function showSendMessageModal() {
            const modal = new bootstrap.Modal(document.getElementById('sendMessageModal'));
            modal.show();
        }

        async function sendMessage() {
            const phone = document.getElementById('message-phone').value;
            const message = document.getElementById('message-text').value;

            if (!phone || !message) {
                showAlert('Preencha todos os campos', 'warning');
                return;
            }

            const result = await apiCall('/send-message', {
                method: 'POST',
                body: JSON.stringify({ phone, message })
            });

            if (result.success) {
                showAlert('Mensagem enviada com sucesso!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('sendMessageModal')).hide();
                document.getElementById('send-message-form').reset();
            } else {
                showAlert('Erro ao enviar mensagem: ' + result.error, 'danger');
            }
        }

        function sendMessageToCustomer(phone) {
            document.getElementById('message-phone').value = phone;
            showSendMessageModal();
        }

        // Broadcast form
        document.getElementById('broadcast-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const message = document.getElementById('broadcast-message').value;
            const phonesText = document.getElementById('broadcast-phones').value;
            const phones = phonesText.split('\n').filter(phone => phone.trim());

            if (!message || phones.length === 0) {
                showAlert('Preencha a mensagem e pelo menos um telefone', 'warning');
                return;
            }

            const result = await apiCall('/broadcast', {
                method: 'POST',
                body: JSON.stringify({ message, phones })
            });

            if (result.success) {
                showAlert(`Broadcast enviado! ${result.data.success} sucessos, ${result.data.errors} erros`, 'success');
                this.reset();
            } else {
                showAlert('Erro no broadcast: ' + result.error, 'danger');
            }
        });
    </script>
</body>
</html>
