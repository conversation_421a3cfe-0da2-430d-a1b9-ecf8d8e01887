const config = require('../../config/settings');
const Database = require('../database/db');

class MessageHandler {
    constructor() {
        this.userSessions = new Map(); // Armazenar sessões de usuários
    }

    async processMessage(messageText, contact) {
        const userPhone = contact.number;
        const message = messageText.toLowerCase().trim();
        
        // Verificar se é uma palavra-chave especial
        if (this.isKeyword(message)) {
            return this.handleKeyword(message, userPhone);
        }

        // Verificar se é uma opção do menu (1-6)
        if (this.isMenuOption(message)) {
            return this.handleMenuOption(message, userPhone);
        }

        // Verificar comandos especiais
        if (message.startsWith('orçamento') || message.startsWith('orcamento')) {
            return this.handleBudgetRequest(messageText, userPhone);
        }

        if (message.startsWith('agendar')) {
            return this.handleScheduleRequest(messageText, userPhone);
        }

        // Se não reconheceu a mensagem, mostrar menu
        return this.showWelcomeMenu(userPhone);
    }

    isKeyword(message) {
        return config.bot.keywords.hasOwnProperty(message);
    }

    handleKeyword(message, userPhone) {
        const action = config.bot.keywords[message];
        
        if (action === 'SHOW_MENU') {
            return this.showWelcomeMenu(userPhone);
        }
        
        return null;
    }

    isMenuOption(message) {
        return /^[1-6]$/.test(message);
    }

    async handleMenuOption(option, userPhone) {
        const menuOption = config.bot.menuOptions[option];
        
        if (menuOption) {
            // Salvar escolha do usuário
            await this.saveUserChoice(userPhone, option, menuOption.title);
            
            return menuOption.message;
        }
        
        return config.bot.invalidOption;
    }

    async handleBudgetRequest(message, userPhone) {
        const budgetText = message.substring(9).trim(); // Remove "orçamento"
        
        // Salvar solicitação de orçamento
        await Database.saveBudgetRequest({
            phone: userPhone,
            request: budgetText,
            timestamp: new Date(),
            status: 'pending'
        });

        return `✅ *Solicitação de Orçamento Recebida!*

📝 *Sua solicitação:* ${budgetText}

⏰ Analisaremos sua necessidade e retornaremos em até 24 horas com um orçamento personalizado.

📞 Para urgências, ligue: (11) 99999-9999

Digite *MENU* para voltar ao menu principal.`;
    }

    async handleScheduleRequest(message, userPhone) {
        const scheduleText = message.substring(8).trim(); // Remove "agendar"
        
        // Salvar solicitação de agendamento
        await Database.saveScheduleRequest({
            phone: userPhone,
            request: scheduleText,
            timestamp: new Date(),
            status: 'pending'
        });

        return `✅ *Solicitação de Agendamento Recebida!*

📅 *Detalhes:* ${scheduleText}

⏰ Confirmaremos seu agendamento em breve via WhatsApp.

📋 *Próximos passos:*
1. Verificaremos disponibilidade
2. Confirmaremos data e horário
3. Enviaremos lembrete 1 dia antes

📞 Para alterações, ligue: (11) 99999-9999

Digite *MENU* para voltar ao menu principal.`;
    }

    showWelcomeMenu(userPhone) {
        // Salvar interação
        this.saveUserInteraction(userPhone, 'menu_shown');
        
        return config.bot.welcomeMessage;
    }

    async saveUserChoice(userPhone, option, title) {
        try {
            await Database.saveUserChoice({
                phone: userPhone,
                option: option,
                title: title,
                timestamp: new Date()
            });
        } catch (error) {
            console.error('❌ Erro ao salvar escolha do usuário:', error);
        }
    }

    async saveUserInteraction(userPhone, interaction) {
        try {
            await Database.saveUserInteraction({
                phone: userPhone,
                interaction: interaction,
                timestamp: new Date()
            });
        } catch (error) {
            console.error('❌ Erro ao salvar interação:', error);
        }
    }

    // Método para obter estatísticas
    async getUserStats(userPhone) {
        try {
            return await Database.getUserStats(userPhone);
        } catch (error) {
            console.error('❌ Erro ao obter estatísticas:', error);
            return null;
        }
    }

    // Método para obter histórico de conversas
    async getConversationHistory(userPhone, limit = 10) {
        try {
            return await Database.getConversationHistory(userPhone, limit);
        } catch (error) {
            console.error('❌ Erro ao obter histórico:', error);
            return [];
        }
    }
}

module.exports = MessageHandler;
