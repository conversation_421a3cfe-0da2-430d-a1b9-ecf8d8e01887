const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const MessageHandler = require('./messageHandler');
const Database = require('../database/db');
const config = require('../../config/settings');

class WhatsAppBot {
    constructor() {
        this.client = new Client({
            authStrategy: new LocalAuth({
                clientId: "whatsapp-business-bot"
            }),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ]
            }
        });

        this.messageHandler = new MessageHandler();
        this.isReady = false;
        
        this.setupEventListeners();
    }

    setupEventListeners() {
        // QR Code para autenticação
        this.client.on('qr', (qr) => {
            console.log('📱 Escaneie o QR Code abaixo com seu WhatsApp:');
            qrcode.generate(qr, { small: true });
            console.log('⚠️  IMPORTANTE: Mantenha seu celular conectado à internet!');
        });

        // Cliente pronto
        this.client.on('ready', () => {
            console.log('✅ WhatsApp conectado com sucesso!');
            this.isReady = true;
        });

        // Nova mensagem recebida
        this.client.on('message', async (message) => {
            try {
                await this.handleMessage(message);
            } catch (error) {
                console.error('❌ Erro ao processar mensagem:', error);
            }
        });

        // Desconectado
        this.client.on('disconnected', (reason) => {
            console.log('⚠️  WhatsApp desconectado:', reason);
            this.isReady = false;
        });

        // Erro de autenticação
        this.client.on('auth_failure', (msg) => {
            console.error('❌ Falha na autenticação:', msg);
        });
    }

    async initialize() {
        try {
            console.log('🔄 Inicializando cliente WhatsApp...');
            await this.client.initialize();
            
            // Aguardar até estar pronto
            while (!this.isReady) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            console.log('✅ Bot WhatsApp inicializado com sucesso!');
        } catch (error) {
            console.error('❌ Erro ao inicializar WhatsApp:', error);
            throw error;
        }
    }

    async handleMessage(message) {
        // Ignorar mensagens próprias e de grupos
        if (message.fromMe || message.from.includes('@g.us')) {
            return;
        }

        const contact = await message.getContact();
        const chat = await message.getChat();
        
        console.log(`📨 Nova mensagem de ${contact.name || contact.number}: ${message.body}`);

        // Salvar mensagem no banco de dados
        await this.saveMessage(message, contact);

        // Processar mensagem
        const response = await this.messageHandler.processMessage(message.body, contact);
        
        if (response) {
            await this.sendMessage(message.from, response);
        }
    }

    async saveMessage(message, contact) {
        try {
            await Database.saveMessage({
                phone: contact.number,
                name: contact.name || 'Sem nome',
                message: message.body,
                timestamp: new Date(),
                type: 'received'
            });
        } catch (error) {
            console.error('❌ Erro ao salvar mensagem:', error);
        }
    }

    async sendMessage(chatId, message) {
        try {
            if (!this.isReady) {
                console.log('⚠️  WhatsApp não está pronto. Mensagem não enviada.');
                return false;
            }

            await this.client.sendMessage(chatId, message);
            console.log(`📤 Mensagem enviada para ${chatId}`);

            // Salvar mensagem enviada no banco
            await Database.saveMessage({
                phone: chatId.replace('@c.us', ''),
                message: message,
                timestamp: new Date(),
                type: 'sent'
            });

            return true;
        } catch (error) {
            console.error('❌ Erro ao enviar mensagem:', error);
            return false;
        }
    }

    async sendScheduledMessage(phoneNumber, message) {
        try {
            const chatId = `${phoneNumber}@c.us`;
            return await this.sendMessage(chatId, message);
        } catch (error) {
            console.error('❌ Erro ao enviar mensagem programada:', error);
            return false;
        }
    }

    async broadcastMessage(message, phoneNumbers = []) {
        const results = [];
        
        for (const phone of phoneNumbers) {
            const result = await this.sendScheduledMessage(phone, message);
            results.push({ phone, success: result });
            
            // Delay entre mensagens para evitar spam
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        return results;
    }

    getStatus() {
        return {
            connected: this.isReady,
            client: this.client ? 'initialized' : 'not_initialized'
        };
    }

    async destroy() {
        try {
            if (this.client) {
                await this.client.destroy();
                console.log('🛑 Cliente WhatsApp encerrado');
            }
        } catch (error) {
            console.error('❌ Erro ao encerrar cliente:', error);
        }
    }
}

module.exports = WhatsAppBot;
