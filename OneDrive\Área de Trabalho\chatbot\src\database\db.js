const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class Database {
    constructor() {
        this.db = null;
        this.dbPath = path.join(__dirname, '../../database/chatbot.db');
    }

    async initialize() {
        try {
            // Criar diretório se não existir
            const dbDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
            }

            // Conectar ao banco
            this.db = new sqlite3.Database(this.dbPath);
            
            // Criar tabelas
            await this.createTables();
            
            console.log('✅ Banco de dados inicializado com sucesso!');
        } catch (error) {
            console.error('❌ Erro ao inicializar banco de dados:', error);
            throw error;
        }
    }

    async createTables() {
        const tables = [
            // Tabela de mensagens
            `CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone TEXT NOT NULL,
                name TEXT,
                message TEXT NOT NULL,
                type TEXT NOT NULL CHECK(type IN ('sent', 'received')),
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Tabela de clientes
            `CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone TEXT UNIQUE NOT NULL,
                name TEXT,
                first_contact DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_contact DATETIME DEFAULT CURRENT_TIMESTAMP,
                total_messages INTEGER DEFAULT 0,
                status TEXT DEFAULT 'active'
            )`,

            // Tabela de escolhas do menu
            `CREATE TABLE IF NOT EXISTS user_choices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone TEXT NOT NULL,
                option TEXT NOT NULL,
                title TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Tabela de solicitações de orçamento
            `CREATE TABLE IF NOT EXISTS budget_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone TEXT NOT NULL,
                request TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                response TEXT,
                response_date DATETIME
            )`,

            // Tabela de agendamentos
            `CREATE TABLE IF NOT EXISTS schedule_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone TEXT NOT NULL,
                request TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                scheduled_date DATETIME,
                notes TEXT
            )`,

            // Tabela de interações
            `CREATE TABLE IF NOT EXISTS user_interactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone TEXT NOT NULL,
                interaction TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (const table of tables) {
            await this.runQuery(table);
        }
    }

    runQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    getQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    allQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    async saveMessage(data) {
        const { phone, name, message, type, timestamp } = data;
        
        // Salvar mensagem
        await this.runQuery(
            'INSERT INTO messages (phone, name, message, type, timestamp) VALUES (?, ?, ?, ?, ?)',
            [phone, name, message, type, timestamp]
        );

        // Atualizar ou criar cliente
        await this.updateCustomer(phone, name);
    }

    async updateCustomer(phone, name) {
        // Verificar se cliente existe
        const customer = await this.getQuery(
            'SELECT * FROM customers WHERE phone = ?',
            [phone]
        );

        if (customer) {
            // Atualizar cliente existente
            await this.runQuery(
                'UPDATE customers SET name = COALESCE(?, name), last_contact = CURRENT_TIMESTAMP, total_messages = total_messages + 1 WHERE phone = ?',
                [name, phone]
            );
        } else {
            // Criar novo cliente
            await this.runQuery(
                'INSERT INTO customers (phone, name, first_contact, last_contact, total_messages) VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1)',
                [phone, name]
            );
        }
    }

    async saveUserChoice(data) {
        const { phone, option, title, timestamp } = data;
        return await this.runQuery(
            'INSERT INTO user_choices (phone, option, title, timestamp) VALUES (?, ?, ?, ?)',
            [phone, option, title, timestamp]
        );
    }

    async saveBudgetRequest(data) {
        const { phone, request, status, timestamp } = data;
        return await this.runQuery(
            'INSERT INTO budget_requests (phone, request, status, timestamp) VALUES (?, ?, ?, ?)',
            [phone, request, status, timestamp]
        );
    }

    async saveScheduleRequest(data) {
        const { phone, request, status, timestamp } = data;
        return await this.runQuery(
            'INSERT INTO schedule_requests (phone, request, status, timestamp) VALUES (?, ?, ?, ?)',
            [phone, request, status, timestamp]
        );
    }

    async saveUserInteraction(data) {
        const { phone, interaction, timestamp } = data;
        return await this.runQuery(
            'INSERT INTO user_interactions (phone, interaction, timestamp) VALUES (?, ?, ?)',
            [phone, interaction, timestamp]
        );
    }

    async getCustomers() {
        return await this.allQuery('SELECT * FROM customers ORDER BY last_contact DESC');
    }

    async getMessages(limit = 100) {
        return await this.allQuery(
            'SELECT * FROM messages ORDER BY timestamp DESC LIMIT ?',
            [limit]
        );
    }

    async getConversationHistory(phone, limit = 10) {
        return await this.allQuery(
            'SELECT * FROM messages WHERE phone = ? ORDER BY timestamp DESC LIMIT ?',
            [phone, limit]
        );
    }

    async getUserStats(phone) {
        const stats = await this.getQuery(`
            SELECT 
                c.name,
                c.first_contact,
                c.last_contact,
                c.total_messages,
                COUNT(DISTINCT uc.option) as menu_options_used,
                COUNT(br.id) as budget_requests,
                COUNT(sr.id) as schedule_requests
            FROM customers c
            LEFT JOIN user_choices uc ON c.phone = uc.phone
            LEFT JOIN budget_requests br ON c.phone = br.phone
            LEFT JOIN schedule_requests sr ON c.phone = sr.phone
            WHERE c.phone = ?
            GROUP BY c.phone
        `, [phone]);

        return stats;
    }

    async getDashboardStats() {
        const totalCustomers = await this.getQuery('SELECT COUNT(*) as count FROM customers');
        const totalMessages = await this.getQuery('SELECT COUNT(*) as count FROM messages');
        const todayMessages = await this.getQuery(`
            SELECT COUNT(*) as count FROM messages 
            WHERE DATE(timestamp) = DATE('now')
        `);
        const pendingBudgets = await this.getQuery(`
            SELECT COUNT(*) as count FROM budget_requests 
            WHERE status = 'pending'
        `);

        return {
            totalCustomers: totalCustomers.count,
            totalMessages: totalMessages.count,
            todayMessages: todayMessages.count,
            pendingBudgets: pendingBudgets.count
        };
    }

    async close() {
        if (this.db) {
            this.db.close();
        }
    }
}

// Singleton instance
const database = new Database();
module.exports = database;
