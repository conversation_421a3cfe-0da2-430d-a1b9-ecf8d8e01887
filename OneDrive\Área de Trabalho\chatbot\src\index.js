const WhatsAppBot = require('./bot/whatsapp');
const CronScheduler = require('./scheduler/cronJobs');
const WebServer = require('./web/server');
const Database = require('./database/db');
const logger = require('./utils/logger');
const config = require('../config/settings');

logger.info('🤖 Iniciando WhatsApp Business Bot...');

async function startBot() {
    try {
        // Inicializar banco de dados
        logger.info('📊 Inicializando banco de dados...');
        await Database.initialize();

        // Inicializar bot do WhatsApp (sem aguardar conexão completa)
        logger.info('📱 Conectando ao WhatsApp...');
        const bot = new WhatsAppBot();

        // Inicializar agendador de mensagens
        logger.info('⏰ Configurando mensagens programadas...');
        const scheduler = new CronScheduler(bot);

        // Inicializar servidor web ANTES da conexão completa do WhatsApp
        logger.info('🌐 Iniciando painel de administração...');
        const webServer = new WebServer(bot, scheduler);
        webServer.start(config.webServer.port);

        logger.info('✅ Servidor web iniciado com sucesso!');
        logger.info(`📊 Painel: http://localhost:${config.webServer.port}`);

        // Agora inicializar o WhatsApp (isso pode demorar)
        await bot.initialize();

        // Iniciar o agendador após WhatsApp estar pronto
        scheduler.start();

        logger.info('✅ Bot WhatsApp conectado com sucesso!');

        // Limpeza de logs antigos na inicialização
        logger.cleanOldLogs(30);

    } catch (error) {
        logger.error('❌ Erro ao iniciar o bot:', error);
        process.exit(1);
    }
}

// Tratamento de sinais para encerramento gracioso
process.on('SIGINT', () => {
    logger.info('\n🛑 Encerrando bot...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    logger.info('\n🛑 Encerrando bot...');
    process.exit(0);
});

// Capturar erros não tratados
process.on('uncaughtException', (error) => {
    logger.error('Erro não capturado:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Promise rejeitada não tratada:', { reason, promise });
});

// Iniciar o bot
startBot();
