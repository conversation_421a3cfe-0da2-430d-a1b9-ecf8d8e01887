const WhatsAppBot = require('./bot/whatsapp');
const CronScheduler = require('./scheduler/cronJobs');
const WebServer = require('./web/server');
const Database = require('./database/db');
const logger = require('./utils/logger');
const config = require('../config/settings');

logger.info('🤖 Iniciando WhatsApp Business Bot...');

async function startBot() {
    try {
        // Inicializar banco de dados
        logger.info('📊 Inicializando banco de dados...');
        await Database.initialize();

        // Inicializar bot do WhatsApp
        logger.info('📱 Conectando ao WhatsApp...');
        const bot = new WhatsAppBot();
        await bot.initialize();

        // Inicializar agendador de mensagens
        logger.info('⏰ Configurando mensagens programadas...');
        const scheduler = new CronScheduler(bot);
        scheduler.start();

        // Inicializar servidor web
        logger.info('🌐 Iniciando painel de administração...');
        const webServer = new WebServer(bot, scheduler);
        webServer.start(config.webServer.port);

        logger.info('✅ Bot iniciado com sucesso!');
        logger.info(`📊 Painel: http://localhost:${config.webServer.port}`);

        // Limpeza de logs antigos na inicialização
        logger.cleanOldLogs(30);

    } catch (error) {
        logger.error('❌ Erro ao iniciar o bot:', error);
        process.exit(1);
    }
}

// Tratamento de sinais para encerramento gracioso
process.on('SIGINT', () => {
    logger.info('\n🛑 Encerrando bot...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    logger.info('\n🛑 Encerrando bot...');
    process.exit(0);
});

// Capturar erros não tratados
process.on('uncaughtException', (error) => {
    logger.error('Erro não capturado:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Promise rejeitada não tratada:', { reason, promise });
});

// Iniciar o bot
startBot();
