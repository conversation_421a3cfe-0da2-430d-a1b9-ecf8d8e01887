const cron = require('node-cron');
const config = require('../../config/settings');
const Database = require('../database/db');

class CronScheduler {
    constructor(whatsappBot) {
        this.bot = whatsappBot;
        this.jobs = new Map();
        this.isRunning = false;
    }

    start() {
        if (!config.scheduledMessages.enabled) {
            console.log('⚠️  Mensagens programadas desabilitadas na configuração');
            return;
        }

        console.log('⏰ Iniciando agendador de mensagens...');
        
        // Configurar jobs das mensagens programadas
        config.scheduledMessages.messages.forEach(messageConfig => {
            if (messageConfig.active) {
                this.scheduleMessage(messageConfig);
            }
        });

        // Job para limpeza de logs antigos (diário às 2h)
        this.scheduleCleanup();

        this.isRunning = true;
        console.log(`✅ ${this.jobs.size} mensagens programadas ativas`);
    }

    scheduleMessage(messageConfig) {
        const { name, cron: cronExpression, message } = messageConfig;

        try {
            // Validar expressão cron
            if (!cron.validate(cronExpression)) {
                console.error(`❌ Expressão cron inválida para "${name}": ${cronExpression}`);
                return;
            }

            const job = cron.schedule(cronExpression, async () => {
                await this.executeScheduledMessage(name, message);
            }, {
                scheduled: false,
                timezone: "America/Sao_Paulo"
            });

            job.start();
            this.jobs.set(name, job);
            
            console.log(`📅 Mensagem programada "${name}" configurada: ${cronExpression}`);
        } catch (error) {
            console.error(`❌ Erro ao configurar mensagem "${name}":`, error);
        }
    }

    async executeScheduledMessage(name, message) {
        try {
            console.log(`⏰ Executando mensagem programada: ${name}`);

            // Obter lista de clientes ativos
            const customers = await this.getActiveCustomers();
            
            if (customers.length === 0) {
                console.log('ℹ️  Nenhum cliente ativo para enviar mensagem programada');
                return;
            }

            console.log(`📤 Enviando mensagem para ${customers.length} clientes...`);

            // Enviar mensagem para cada cliente
            let successCount = 0;
            let errorCount = 0;

            for (const customer of customers) {
                try {
                    const success = await this.bot.sendScheduledMessage(customer.phone, message);
                    
                    if (success) {
                        successCount++;
                        console.log(`✅ Mensagem enviada para ${customer.phone}`);
                    } else {
                        errorCount++;
                        console.log(`❌ Falha ao enviar para ${customer.phone}`);
                    }

                    // Delay entre mensagens para evitar spam
                    await this.delay(2000);
                    
                } catch (error) {
                    errorCount++;
                    console.error(`❌ Erro ao enviar para ${customer.phone}:`, error);
                }
            }

            // Log do resultado
            console.log(`📊 Mensagem "${name}" executada: ${successCount} sucessos, ${errorCount} erros`);

            // Salvar estatísticas
            await this.saveScheduledMessageStats(name, successCount, errorCount);

        } catch (error) {
            console.error(`❌ Erro ao executar mensagem programada "${name}":`, error);
        }
    }

    async getActiveCustomers() {
        try {
            // Obter clientes que interagiram nos últimos 30 dias
            const customers = await Database.allQuery(`
                SELECT DISTINCT phone, name 
                FROM customers 
                WHERE status = 'active' 
                AND last_contact >= datetime('now', '-30 days')
                ORDER BY last_contact DESC
            `);

            return customers;
        } catch (error) {
            console.error('❌ Erro ao obter clientes ativos:', error);
            return [];
        }
    }

    async saveScheduledMessageStats(messageName, successCount, errorCount) {
        try {
            await Database.runQuery(`
                INSERT INTO scheduled_message_stats 
                (message_name, success_count, error_count, executed_at) 
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            `, [messageName, successCount, errorCount]);
        } catch (error) {
            // Se a tabela não existir, criar ela
            if (error.message.includes('no such table')) {
                await this.createStatsTable();
                // Tentar novamente
                await Database.runQuery(`
                    INSERT INTO scheduled_message_stats 
                    (message_name, success_count, error_count, executed_at) 
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                `, [messageName, successCount, errorCount]);
            } else {
                console.error('❌ Erro ao salvar estatísticas:', error);
            }
        }
    }

    async createStatsTable() {
        await Database.runQuery(`
            CREATE TABLE IF NOT EXISTS scheduled_message_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                message_name TEXT NOT NULL,
                success_count INTEGER DEFAULT 0,
                error_count INTEGER DEFAULT 0,
                executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);
    }

    scheduleCleanup() {
        // Limpeza diária às 2h da manhã
        const cleanupJob = cron.schedule('0 2 * * *', async () => {
            await this.cleanupOldData();
        }, {
            scheduled: false,
            timezone: "America/Sao_Paulo"
        });

        cleanupJob.start();
        this.jobs.set('cleanup', cleanupJob);
        console.log('🧹 Job de limpeza configurado para 2h diariamente');
    }

    async cleanupOldData() {
        try {
            console.log('🧹 Iniciando limpeza de dados antigos...');

            // Remover mensagens antigas (mais de 90 dias)
            const result = await Database.runQuery(`
                DELETE FROM messages 
                WHERE timestamp < datetime('now', '-90 days')
            `);

            console.log(`🗑️  ${result.changes} mensagens antigas removidas`);

            // Remover estatísticas antigas (mais de 180 dias)
            await Database.runQuery(`
                DELETE FROM scheduled_message_stats 
                WHERE executed_at < datetime('now', '-180 days')
            `);

            console.log('✅ Limpeza concluída');
        } catch (error) {
            console.error('❌ Erro na limpeza:', error);
        }
    }

    // Método para adicionar nova mensagem programada
    addScheduledMessage(name, cronExpression, message) {
        const messageConfig = { name, cron: cronExpression, message, active: true };
        this.scheduleMessage(messageConfig);
    }

    // Método para remover mensagem programada
    removeScheduledMessage(name) {
        const job = this.jobs.get(name);
        if (job) {
            job.destroy();
            this.jobs.delete(name);
            console.log(`🗑️  Mensagem programada "${name}" removida`);
            return true;
        }
        return false;
    }

    // Método para listar mensagens ativas
    getActiveJobs() {
        return Array.from(this.jobs.keys());
    }

    // Método para parar todas as mensagens
    stop() {
        console.log('🛑 Parando agendador de mensagens...');
        
        this.jobs.forEach((job, name) => {
            job.destroy();
            console.log(`🛑 Job "${name}" parado`);
        });
        
        this.jobs.clear();
        this.isRunning = false;
        console.log('✅ Agendador parado');
    }

    // Método utilitário para delay
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Status do agendador
    getStatus() {
        return {
            running: this.isRunning,
            activeJobs: this.jobs.size,
            jobNames: Array.from(this.jobs.keys())
        };
    }
}

module.exports = CronScheduler;
