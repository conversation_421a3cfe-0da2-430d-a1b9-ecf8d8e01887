const fs = require('fs');
const path = require('path');
const config = require('../../config/settings');

class Logger {
    constructor() {
        this.logDir = config.logging.directory || './logs';
        this.level = config.logging.level || 'info';
        this.enabled = config.logging.enabled !== false;
        
        // Criar diretório de logs se não existir
        this.ensureLogDirectory();
        
        // Níveis de log
        this.levels = {
            debug: 0,
            info: 1,
            warn: 2,
            error: 3
        };
    }

    ensureLogDirectory() {
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }
    }

    shouldLog(level) {
        return this.enabled && this.levels[level] >= this.levels[this.level];
    }

    formatMessage(level, message, data = null) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level: level.toUpperCase(),
            message,
            data
        };

        return JSON.stringify(logEntry);
    }

    writeToFile(level, formattedMessage) {
        const today = new Date().toISOString().split('T')[0];
        const filename = `${today}.log`;
        const filepath = path.join(this.logDir, filename);

        try {
            fs.appendFileSync(filepath, formattedMessage + '\n');
        } catch (error) {
            console.error('Erro ao escrever no arquivo de log:', error);
        }
    }

    log(level, message, data = null) {
        if (!this.shouldLog(level)) {
            return;
        }

        const formattedMessage = this.formatMessage(level, message, data);
        
        // Escrever no arquivo
        this.writeToFile(level, formattedMessage);
        
        // Também exibir no console com cores
        this.logToConsole(level, message, data);
    }

    logToConsole(level, message, data) {
        const timestamp = new Date().toLocaleString('pt-BR');
        const colors = {
            debug: '\x1b[36m', // Cyan
            info: '\x1b[32m',  // Green
            warn: '\x1b[33m',  // Yellow
            error: '\x1b[31m'  // Red
        };
        const reset = '\x1b[0m';
        
        const color = colors[level] || '';
        const prefix = `${color}[${timestamp}] ${level.toUpperCase()}:${reset}`;
        
        if (data) {
            console.log(`${prefix} ${message}`, data);
        } else {
            console.log(`${prefix} ${message}`);
        }
    }

    debug(message, data = null) {
        this.log('debug', message, data);
    }

    info(message, data = null) {
        this.log('info', message, data);
    }

    warn(message, data = null) {
        this.log('warn', message, data);
    }

    error(message, data = null) {
        this.log('error', message, data);
    }

    // Métodos específicos para o bot
    logMessage(type, phone, message, name = null) {
        this.info(`Mensagem ${type}`, {
            phone,
            name,
            message: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
            type
        });
    }

    logBotEvent(event, details = null) {
        this.info(`Bot Event: ${event}`, details);
    }

    logScheduledMessage(messageName, recipientCount, successCount, errorCount) {
        this.info(`Mensagem programada executada: ${messageName}`, {
            recipients: recipientCount,
            success: successCount,
            errors: errorCount
        });
    }

    logError(error, context = null) {
        this.error('Erro no sistema', {
            error: error.message,
            stack: error.stack,
            context
        });
    }

    // Método para obter logs recentes
    getRecentLogs(lines = 100) {
        try {
            const today = new Date().toISOString().split('T')[0];
            const filename = `${today}.log`;
            const filepath = path.join(this.logDir, filename);

            if (!fs.existsSync(filepath)) {
                return [];
            }

            const content = fs.readFileSync(filepath, 'utf8');
            const logLines = content.trim().split('\n');
            
            return logLines
                .slice(-lines)
                .map(line => {
                    try {
                        return JSON.parse(line);
                    } catch {
                        return { message: line, level: 'INFO', timestamp: new Date().toISOString() };
                    }
                })
                .reverse();
        } catch (error) {
            this.error('Erro ao ler logs', error);
            return [];
        }
    }

    // Método para limpar logs antigos
    cleanOldLogs(daysToKeep = 30) {
        try {
            const files = fs.readdirSync(this.logDir);
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            files.forEach(file => {
                if (file.endsWith('.log')) {
                    const filePath = path.join(this.logDir, file);
                    const stats = fs.statSync(filePath);
                    
                    if (stats.mtime < cutoffDate) {
                        fs.unlinkSync(filePath);
                        this.info(`Log antigo removido: ${file}`);
                    }
                }
            });
        } catch (error) {
            this.error('Erro ao limpar logs antigos', error);
        }
    }

    // Método para obter estatísticas de logs
    getLogStats() {
        try {
            const today = new Date().toISOString().split('T')[0];
            const filename = `${today}.log`;
            const filepath = path.join(this.logDir, filename);

            if (!fs.existsSync(filepath)) {
                return {
                    total: 0,
                    debug: 0,
                    info: 0,
                    warn: 0,
                    error: 0
                };
            }

            const content = fs.readFileSync(filepath, 'utf8');
            const lines = content.trim().split('\n');
            
            const stats = {
                total: lines.length,
                debug: 0,
                info: 0,
                warn: 0,
                error: 0
            };

            lines.forEach(line => {
                try {
                    const logEntry = JSON.parse(line);
                    const level = logEntry.level.toLowerCase();
                    if (stats.hasOwnProperty(level)) {
                        stats[level]++;
                    }
                } catch {
                    // Ignorar linhas inválidas
                }
            });

            return stats;
        } catch (error) {
            this.error('Erro ao obter estatísticas de logs', error);
            return {
                total: 0,
                debug: 0,
                info: 0,
                warn: 0,
                error: 0
            };
        }
    }
}

// Singleton instance
const logger = new Logger();
module.exports = logger;
