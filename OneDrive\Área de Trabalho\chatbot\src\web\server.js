const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const Database = require('../database/db');

class WebServer {
    constructor(whatsappBot, scheduler) {
        this.app = express();
        this.bot = whatsappBot;
        this.scheduler = scheduler;
        this.server = null;
        
        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        // CORS
        this.app.use(cors());
        
        // Body parser
        this.app.use(bodyParser.json());
        this.app.use(bodyParser.urlencoded({ extended: true }));
        
        // Servir arquivos estáticos
        this.app.use(express.static(path.join(__dirname, '../../public')));
    }

    setupRoutes() {
        // Rota principal - Dashboard
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, '../../public/index.html'));
        });

        // API Routes
        this.app.get('/api/status', this.getStatus.bind(this));
        this.app.get('/api/dashboard', this.getDashboard.bind(this));
        this.app.get('/api/customers', this.getCustomers.bind(this));
        this.app.get('/api/messages', this.getMessages.bind(this));
        this.app.get('/api/conversations/:phone', this.getConversation.bind(this));
        
        // Mensagens
        this.app.post('/api/send-message', this.sendMessage.bind(this));
        this.app.post('/api/broadcast', this.broadcastMessage.bind(this));
        
        // Agendador
        this.app.get('/api/scheduler/status', this.getSchedulerStatus.bind(this));
        this.app.post('/api/scheduler/add', this.addScheduledMessage.bind(this));
        this.app.delete('/api/scheduler/:name', this.removeScheduledMessage.bind(this));
        
        // Relatórios
        this.app.get('/api/reports/daily', this.getDailyReport.bind(this));
        this.app.get('/api/reports/weekly', this.getWeeklyReport.bind(this));
    }

    async getStatus(req, res) {
        try {
            const botStatus = this.bot.getStatus();
            const schedulerStatus = this.scheduler.getStatus();
            
            res.json({
                success: true,
                data: {
                    bot: botStatus,
                    scheduler: schedulerStatus,
                    server: {
                        uptime: process.uptime(),
                        memory: process.memoryUsage()
                    }
                }
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    async getDashboard(req, res) {
        try {
            const stats = await Database.getDashboardStats();
            
            res.json({
                success: true,
                data: stats
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    async getCustomers(req, res) {
        try {
            const customers = await Database.getCustomers();
            
            res.json({
                success: true,
                data: customers
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    async getMessages(req, res) {
        try {
            const limit = parseInt(req.query.limit) || 100;
            const messages = await Database.getMessages(limit);
            
            res.json({
                success: true,
                data: messages
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    async getConversation(req, res) {
        try {
            const { phone } = req.params;
            const limit = parseInt(req.query.limit) || 50;
            
            const messages = await Database.getConversationHistory(phone, limit);
            const stats = await Database.getUserStats(phone);
            
            res.json({
                success: true,
                data: {
                    messages,
                    stats
                }
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    async sendMessage(req, res) {
        try {
            const { phone, message } = req.body;
            
            if (!phone || !message) {
                return res.status(400).json({
                    success: false,
                    error: 'Telefone e mensagem são obrigatórios'
                });
            }

            const success = await this.bot.sendScheduledMessage(phone, message);
            
            res.json({
                success,
                message: success ? 'Mensagem enviada com sucesso' : 'Falha ao enviar mensagem'
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    async broadcastMessage(req, res) {
        try {
            const { message, phones } = req.body;
            
            if (!message || !phones || !Array.isArray(phones)) {
                return res.status(400).json({
                    success: false,
                    error: 'Mensagem e lista de telefones são obrigatórios'
                });
            }

            const results = await this.bot.broadcastMessage(message, phones);
            
            const successCount = results.filter(r => r.success).length;
            const errorCount = results.filter(r => !r.success).length;
            
            res.json({
                success: true,
                data: {
                    total: results.length,
                    success: successCount,
                    errors: errorCount,
                    results
                }
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    async getSchedulerStatus(req, res) {
        try {
            const status = this.scheduler.getStatus();
            
            res.json({
                success: true,
                data: status
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    async addScheduledMessage(req, res) {
        try {
            const { name, cron, message } = req.body;
            
            if (!name || !cron || !message) {
                return res.status(400).json({
                    success: false,
                    error: 'Nome, expressão cron e mensagem são obrigatórios'
                });
            }

            this.scheduler.addScheduledMessage(name, cron, message);
            
            res.json({
                success: true,
                message: 'Mensagem programada adicionada com sucesso'
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    async removeScheduledMessage(req, res) {
        try {
            const { name } = req.params;
            
            const removed = this.scheduler.removeScheduledMessage(name);
            
            if (removed) {
                res.json({
                    success: true,
                    message: 'Mensagem programada removida com sucesso'
                });
            } else {
                res.status(404).json({
                    success: false,
                    error: 'Mensagem programada não encontrada'
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    async getDailyReport(req, res) {
        try {
            const today = new Date().toISOString().split('T')[0];
            
            const report = await Database.allQuery(`
                SELECT 
                    DATE(timestamp) as date,
                    COUNT(*) as total_messages,
                    COUNT(CASE WHEN type = 'received' THEN 1 END) as received_messages,
                    COUNT(CASE WHEN type = 'sent' THEN 1 END) as sent_messages,
                    COUNT(DISTINCT phone) as unique_customers
                FROM messages 
                WHERE DATE(timestamp) = ?
                GROUP BY DATE(timestamp)
            `, [today]);
            
            res.json({
                success: true,
                data: report[0] || {
                    date: today,
                    total_messages: 0,
                    received_messages: 0,
                    sent_messages: 0,
                    unique_customers: 0
                }
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    async getWeeklyReport(req, res) {
        try {
            const report = await Database.allQuery(`
                SELECT 
                    DATE(timestamp) as date,
                    COUNT(*) as total_messages,
                    COUNT(CASE WHEN type = 'received' THEN 1 END) as received_messages,
                    COUNT(CASE WHEN type = 'sent' THEN 1 END) as sent_messages,
                    COUNT(DISTINCT phone) as unique_customers
                FROM messages 
                WHERE timestamp >= datetime('now', '-7 days')
                GROUP BY DATE(timestamp)
                ORDER BY date DESC
            `);
            
            res.json({
                success: true,
                data: report
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    start(port = 3000) {
        this.server = this.app.listen(port, () => {
            console.log(`🌐 Servidor web iniciado na porta ${port}`);
            console.log(`📊 Painel: http://localhost:${port}`);
        });
    }

    stop() {
        if (this.server) {
            this.server.close();
            console.log('🛑 Servidor web encerrado');
        }
    }
}

module.exports = WebServer;
